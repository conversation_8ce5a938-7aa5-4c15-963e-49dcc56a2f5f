{"version": "2.0.0", "tasks": [{"label": "Start Metro", "type": "shell", "command": "npm", "args": ["start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": []}, {"label": "Run Android", "type": "shell", "command": "npm", "args": ["run", "android"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "dependsOn": "Start Metro"}, {"label": "Run iOS", "type": "shell", "command": "npm", "args": ["run", "ios"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "dependsOn": "Start Metro"}, {"label": "<PERSON><PERSON>", "type": "shell", "command": "npm", "args": ["run", "lint"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Lint Fix", "type": "shell", "command": "npm", "args": ["run", "lint", "--", "--fix"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Type Check", "type": "shell", "command": "npx", "args": ["tsc", "--noEmit"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Test", "type": "shell", "command": "npm", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Test Watch", "type": "shell", "command": "npm", "args": ["test", "--", "--watch"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true}, {"label": "Clean", "type": "shell", "command": "npm", "args": ["run", "clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}