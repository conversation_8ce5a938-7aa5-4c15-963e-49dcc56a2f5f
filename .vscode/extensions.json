{
  "recommendations": [
    // 必需插件
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",

    // React Native开发
    "msjsdiag.vscode-react-native",
    "ms-vscode.vscode-react-native",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",

    // Git相关
    "eamodio.gitlens",
    "github.vscode-pull-request-github",
    "donjayamanne.githistory",

    // 代码质量
    "streetsidesoftware.code-spell-checker",
    "usernamehw.errorlens",
    "oderwat.indent-rainbow",
    "aaron-bond.better-comments",

    // 实用工具
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-markdown",
    "yzhang.markdown-all-in-one",
    "shd101wyy.markdown-preview-enhanced",

    // 主题和图标
    "pkief.material-icon-theme",
    "github.github-vscode-theme",

    // 调试和测试
    "ms-vscode.vscode-jest",
    "hbenl.vscode-test-explorer",

    // 其他有用插件
    "ms-vscode.vscode-todo-highlight",
    "gruntfuggly.todo-tree",
    "alefragnani.bookmarks",
    "ms-vscode.vscode-color-info"
  ],
  "unwantedRecommendations": ["ms-vscode.vscode-typescript", "hookyqr.beautify", "ms-vscode.vscode-css"]
}
