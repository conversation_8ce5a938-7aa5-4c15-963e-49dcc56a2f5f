# Git 分支策略

## 🌳 分支模型 - GitFlow

我们采用 GitFlow 分支模型来管理代码版本和发布流程。

### 📋 分支类型

#### 🏠 主要分支

- **main**: 生产环境分支
  - 始终保持稳定可发布状态
  - 只接受来自 release 和 hotfix 分支的合并
  - 每次合并都会触发生产部署

- **develop**: 开发集成分支
  - 包含最新的开发功能
  - 功能分支的合并目标
  - 定期合并到 release 分支

#### 🔧 支持分支

- **feature/**: 功能开发分支
  - 从 develop 分支创建
  - 完成后合并回 develop
  - 命名规范: `feature/task-01-user-auth`

- **release/**: 发布准备分支
  - 从 develop 分支创建
  - 用于发布前的最后调整
  - 完成后合并到 main 和 develop
  - 命名规范: `release/v1.0.0`

- **hotfix/**: 紧急修复分支
  - 从 main 分支创建
  - 用于修复生产环境的紧急问题
  - 完成后合并到 main 和 develop
  - 命名规范: `hotfix/fix-critical-bug`

## 🔄 工作流程

### 🆕 新功能开发

```bash
# 1. 从develop创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/task-01-user-auth

# 2. 开发功能
# ... 编码和提交 ...

# 3. 推送分支
git push origin feature/task-01-user-auth

# 4. 创建Pull Request到develop
# 5. 代码审查和合并
```

### 🚀 发布流程

```bash
# 1. 从develop创建release分支
git checkout develop
git pull origin develop
git checkout -b release/v1.0.0

# 2. 发布准备工作
# - 更新版本号
# - 最后的bug修复
# - 文档更新

# 3. 合并到main
git checkout main
git merge --no-ff release/v1.0.0
git tag -a v1.0.0 -m "Release version 1.0.0"

# 4. 合并回develop
git checkout develop
git merge --no-ff release/v1.0.0

# 5. 删除release分支
git branch -d release/v1.0.0
```

### 🚨 紧急修复

```bash
# 1. 从main创建hotfix分支
git checkout main
git pull origin main
git checkout -b hotfix/fix-critical-bug

# 2. 修复问题
# ... 编码和提交 ...

# 3. 合并到main
git checkout main
git merge --no-ff hotfix/fix-critical-bug
git tag -a v1.0.1 -m "Hotfix version 1.0.1"

# 4. 合并到develop
git checkout develop
git merge --no-ff hotfix/fix-critical-bug

# 5. 删除hotfix分支
git branch -d hotfix/fix-critical-bug
```

## 📝 提交规范

### 🏷️ 提交消息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 📋 类型说明

- `feat`: 新功能
- `fix`: bug 修复
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 重构
- `perf`: 性能优化
- `test`: 测试相关
- `build`: 构建相关
- `ci`: CI/CD 相关
- `chore`: 其他杂项

### 💡 示例

```
feat(auth): add social login functionality

- Implement WeChat login integration
- Add Alipay login support
- Update login UI components

Closes #123
```

## 🔒 分支保护规则

### main 分支

- 禁止直接推送
- 需要 Pull Request
- 需要至少 1 个审查者批准
- 需要通过 CI 检查
- 需要分支是最新的

### develop 分支

- 禁止直接推送
- 需要 Pull Request
- 需要通过 CI 检查

## 🏷️ 版本标签

- 使用语义化版本: `v<major>.<minor>.<patch>`
- 主版本: 不兼容的 API 修改
- 次版本: 向后兼容的功能性新增
- 修订版本: 向后兼容的问题修正

## 🧹 分支清理

- 功能分支合并后及时删除
- 定期清理远程已删除的本地分支
- 保持分支列表整洁

```bash
# 清理已删除的远程分支
git remote prune origin

# 删除已合并的本地分支
git branch --merged | grep -v "\*\|main\|develop" | xargs -n 1 git branch -d
```
