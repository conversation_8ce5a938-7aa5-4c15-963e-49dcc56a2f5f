# Pull Request

## 📝 描述

请简要描述此 PR 的目的和内容。

## 🔗 相关 Issue

- Closes #(issue number)
- Related to #(issue number)

## 📋 变更类型

请勾选适用的选项：

- [ ] 🐛 Bug 修复
- [ ] ✨ 新功能
- [ ] 💄 UI/样式更新
- [ ] ♻️ 代码重构
- [ ] 📝 文档更新
- [ ] 🚀 性能优化
- [ ] 🧪 测试相关
- [ ] 🔧 构建/工具相关
- [ ] 🔥 移除代码/文件

## 🧪 测试

请描述你如何测试了这些变更：

- [ ] 单元测试
- [ ] 集成测试
- [ ] 手动测试
- [ ] 无需测试

测试详情：

<!-- 描述测试步骤和结果 -->

## 📱 平台测试

- [ ] iOS
- [ ] Android
- [ ] 两个平台都测试过

## 📸 截图/录屏

如果适用，请添加截图或录屏来展示变更效果。

## ✅ 检查清单

- [ ] 我的代码遵循了项目的代码规范
- [ ] 我已经进行了自我代码审查
- [ ] 我已经为我的代码添加了注释，特别是在难以理解的地方
- [ ] 我已经对应地更新了文档
- [ ] 我的变更没有产生新的警告
- [ ] 我已经添加了测试来证明我的修复是有效的或者我的功能是正常工作的
- [ ] 新的和现有的单元测试都通过了我的变更
- [ ] 任何依赖的变更都已经被合并和发布

## 🔄 后续工作

如果有相关的后续工作，请在此列出：

- [ ] 任务 1
- [ ] 任务 2

## 📚 其他信息

添加任何其他相关信息或上下文。
