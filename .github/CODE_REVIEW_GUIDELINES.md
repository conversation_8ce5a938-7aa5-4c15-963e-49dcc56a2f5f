# 代码审查指南

## 🎯 代码审查目标

- 确保代码质量和一致性
- 分享知识和最佳实践
- 发现潜在的 bug 和安全问题
- 提高代码可维护性

## 📋 审查检查清单

### 🔍 代码质量

- [ ] 代码遵循项目的编码规范
- [ ] 变量和函数命名清晰有意义
- [ ] 代码逻辑清晰易懂
- [ ] 没有重复代码
- [ ] 错误处理适当

### 🏗️ 架构和设计

- [ ] 代码结构合理
- [ ] 组件职责单一
- [ ] 依赖关系清晰
- [ ] 遵循 SOLID 原则

### 🚀 性能

- [ ] 没有明显的性能问题
- [ ] 适当使用缓存
- [ ] 避免不必要的重渲染
- [ ] 内存使用合理

### 🔒 安全性

- [ ] 输入验证充分
- [ ] 敏感信息处理安全
- [ ] 权限检查适当
- [ ] 没有安全漏洞

### 🧪 测试

- [ ] 关键功能有测试覆盖
- [ ] 测试用例充分
- [ ] 测试代码质量良好
- [ ] 边界条件考虑周全

### 📝 文档

- [ ] 复杂逻辑有注释
- [ ] API 文档完整
- [ ] README 更新及时
- [ ] 变更日志记录

## 💬 审查反馈指南

### ✅ 好的反馈

- 具体明确
- 建设性建议
- 提供替代方案
- 解释原因

### ❌ 避免的反馈

- 过于主观
- 不够具体
- 只批评不建议
- 风格偏好争论

## 🔄 审查流程

1. **自我审查**: 提交前先自己审查代码
2. **同行审查**: 至少一个同事审查
3. **讨论解决**: 对反馈进行讨论和修改
4. **最终批准**: 所有问题解决后批准合并

## 📊 审查优先级

### 🔴 必须修复

- 功能性 bug
- 安全漏洞
- 性能问题
- 架构违反

### 🟡 建议修复

- 代码风格
- 命名改进
- 注释补充
- 小的重构

### 🟢 可选修复

- 个人偏好
- 微小优化
- 风格统一

## 🎯 审查时间建议

- 小 PR (< 100 行): 15-30 分钟
- 中 PR (100-500 行): 30-60 分钟
- 大 PR (> 500 行): 1-2 小时

## 📚 参考资源

- [React Native 最佳实践](https://reactnative.dev/docs/performance)
- [TypeScript 编码规范](https://typescript-eslint.io/rules/)
- [Git 提交规范](https://www.conventionalcommits.org/)
