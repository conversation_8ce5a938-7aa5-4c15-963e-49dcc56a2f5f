{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    // 保持核心类型安全
    "strict": true,
    "noImplicitAny": false,
    "noImplicitReturns": false,
    "noImplicitThis": false,

    // 放宽未使用变量检查（开发阶段很常见）
    "noUnusedLocals": false,
    "noUnusedParameters": false,

    // 关闭过于严格的可选属性检查
    "exactOptionalPropertyTypes": false,
    "noUncheckedIndexedAccess": false,
    "noImplicitOverride": false,

    // 支持动态导入
    "module": "esnext",
    "target": "es2020",
    "moduleResolution": "bundler",

    // 保持路径映射
    "baseUrl": "./src",
    "paths": {
      "@/*": ["*"],
      "@/components/*": ["components/*"],
      "@/screens/*": ["screens/*"],
      "@/services/*": ["services/*"],
      "@/hooks/*": ["hooks/*"],
      "@/utils/*": ["utils/*"],
      "@/types/*": ["types/*"],
      "@/store/*": ["store/*"],
      "@/navigation/*": ["navigation/*"]
    },

    // 保持基本的类型检查
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "isolatedModules": true
  },
  "include": ["src/**/*", "App.tsx", "index.js"],
  "exclude": ["node_modules", "android", "ios"]
}
