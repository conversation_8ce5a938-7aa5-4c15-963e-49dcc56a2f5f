import { Logger } from '../config/logger';
import { addSentryBreadcrumb } from '../config/sentry';

// 性能监控类
class PerformanceMonitor {
  private timers: Map<string, number> = new Map();
  private metrics: Map<string, number[]> = new Map();

  // 开始计时
  startTimer(name: string): void {
    this.timers.set(name, Date.now());
    Logger.debug(`Performance timer started: ${name}`);
  }

  // 结束计时并记录
  endTimer(name: string): number {
    const startTime = this.timers.get(name);
    if (!startTime) {
      Logger.warn(`Performance timer not found: ${name}`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(name);

    // 记录到指标中
    const existing = this.metrics.get(name) || [];
    existing.push(duration);
    this.metrics.set(name, existing);

    Logger.performance(name, duration);
    addSentryBreadcrumb(`Performance: ${name}`, 'performance', 'info', {
      duration,
    });

    return duration;
  }

  // 获取指标统计
  getMetrics(
    name: string,
  ): { avg: number; min: number; max: number; count: number } | null {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) {
      return null;
    }

    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    return { avg, min, max, count: values.length };
  }

  // 清除指标
  clearMetrics(name?: string): void {
    if (name) {
      this.metrics.delete(name);
    } else {
      this.metrics.clear();
    }
  }

  // 获取所有指标
  getAllMetrics(): Record<
    string,
    { avg: number; min: number; max: number; count: number }
  > {
    const result: Record<
      string,
      { avg: number; min: number; max: number; count: number }
    > = {};

    this.metrics.forEach((_, name) => {
      const metrics = this.getMetrics(name);
      if (metrics) {
        result[name] = metrics;
      }
    });

    return result;
  }
}

// 单例实例
export const performanceMonitor = new PerformanceMonitor();

// 装饰器函数用于自动监控函数性能
export const withPerformanceMonitoring = <
  T extends (...args: unknown[]) => unknown,
>(
  name: string,
  fn: T,
): T =>
  ((...args: Parameters<T>) => {
    performanceMonitor.startTimer(name);

    try {
      const result = fn(...args);

      // 如果是Promise，等待完成后记录
      if (result instanceof Promise) {
        return result
          .then(res => {
            performanceMonitor.endTimer(name);
            return res;
          })
          .catch(error => {
            performanceMonitor.endTimer(name);
            throw error;
          });
      }

      performanceMonitor.endTimer(name);
      return result;
    } catch (error) {
      performanceMonitor.endTimer(name);
      throw error;
    }
  }) as T;

// React Hook用于组件性能监控
export const usePerformanceMonitoring = (componentName: string) => {
  const startTime = Date.now();

  return {
    onMount: () => {
      const mountTime = Date.now() - startTime;
      Logger.performance(`${componentName} mount`, mountTime);
    },

    onUnmount: () => {
      Logger.performance(`${componentName} unmount`, Date.now() - startTime);
    },

    measureRender: (renderName: string) => {
      performanceMonitor.startTimer(`${componentName}.${renderName}`);

      return () => {
        performanceMonitor.endTimer(`${componentName}.${renderName}`);
      };
    },
  };
};

// 内存使用监控
export const getMemoryUsage = (): { used: number; total: number } | null => {
  try {
    const perf: any = global.performance;
    if (perf && perf.memory) {
      return {
        used: perf.memory.usedJSHeapSize,
        total: perf.memory.totalJSHeapSize,
      };
    }
  } catch (error) {
    // 忽略错误，返回null
  }
  return null;
};

// FPS监控（简化版）
class FPSMonitor {
  private frameCount = 0;
  private lastTime = Date.now();
  private fps = 60;
  private isRunning = false;

  start(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    this.frameCount = 0;
    this.lastTime = Date.now();

    const loop = () => {
      if (!this.isRunning) return;

      this.frameCount++;
      const currentTime = Date.now();

      if (currentTime - this.lastTime >= 1000) {
        this.fps = this.frameCount;
        this.frameCount = 0;
        this.lastTime = currentTime;

        Logger.debug(`FPS: ${this.fps}`);

        // 如果FPS过低，记录警告
        if (this.fps < 30) {
          Logger.warn(`Low FPS detected: ${this.fps}`);
          addSentryBreadcrumb('Low FPS', 'performance', 'warning', {
            fps: this.fps,
          });
        }
      }

      requestAnimationFrame(loop);
    };

    requestAnimationFrame(loop);
  }

  stop(): void {
    this.isRunning = false;
  }

  getCurrentFPS(): number {
    return this.fps;
  }
}

export const fpsMonitor = new FPSMonitor();

// 启动性能监控
export const startPerformanceMonitoring = (): void => {
  if (__DEV__) {
    fpsMonitor.start();
    Logger.info('Performance monitoring started');
  }
};

// 停止性能监控
export const stopPerformanceMonitoring = (): void => {
  fpsMonitor.stop();
  Logger.info('Performance monitoring stopped');
};
