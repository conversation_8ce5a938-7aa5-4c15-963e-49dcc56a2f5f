// 全局类型声明

declare global {
  // Reactotron类型扩展
  interface Console {
    tron?: {
      log: (...args: unknown[]) => void;
      warn: (...args: unknown[]) => void;
      error: (...args: unknown[]) => void;
      display: (config: {
        name: string;
        value: unknown;
        preview?: string;
      }) => void;
      clear?: () => void;
    };
  }

  // React Native全局变量
  // eslint-disable-next-line no-var
  var __DEV__: boolean;
  // eslint-disable-next-line no-var
  var HermesInternal: unknown;

  // 性能监控扩展
  interface Performance {
    memory?: {
      usedJSHeapSize: number;
      totalJSHeapSize: number;
      jsHeapSizeLimit: number;
    };
  }

  // 调试工具
  // eslint-disable-next-line no-var
  var __DEBUG__: {
    showComponentBoundaries: () => void;
    showPerformanceMetrics: () => void;
    clearLogs: () => void;
    simulateError: (message?: string) => void;
    logMemoryUsage: () => void;
  };

  // React Native Bridge
  // eslint-disable-next-line no-var
  var RN$Bridgeless: boolean | undefined;
}

export {};
