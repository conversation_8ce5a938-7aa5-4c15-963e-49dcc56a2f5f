// 全局类型定义
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  category: string;
  stock: number;
  rating: number;
  reviews: number;
}

export interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  selectedVariant?: string;
}

export interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// 导航类型定义
export type RootStackParamList = {
  // 主要导航
  Main: undefined;

  // 认证相关
  Auth: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;

  // 模态页面
  ProductDetail: { productId: string };
  Search: { query?: string };
  Settings: undefined;
  Notifications: undefined;

  // 其他页面
  WebView: { url: string; title?: string };
};

export type MainTabParamList = {
  Home: undefined;
  Categories: undefined;
  Cart: undefined;
  Social: undefined;
  Profile: undefined;
};

export type DrawerParamList = {
  MainTabs: undefined;
  Orders: undefined;
  Favorites: undefined;
  Settings: undefined;
  Help: undefined;
  About: undefined;
};

export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  ResetPassword: { token: string };
};

// 导航相关的通用类型
export interface NavigationProps {
  navigation: any;
  route: any;
}

export interface ScreenOptions {
  headerShown?: boolean;
  title?: string;
  headerStyle?: object;
  headerTitleStyle?: object;
  headerTintColor?: string;
  gestureEnabled?: boolean;
  animationEnabled?: boolean;
}
