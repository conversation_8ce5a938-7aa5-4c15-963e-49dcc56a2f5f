/**
 * 主题上下文 - 提供主题切换和管理功能
 * 支持系统主题跟随和手动切换
 */

import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { Appearance } from 'react-native';
import { MMKV } from 'react-native-mmkv';
import { PaperProvider } from 'react-native-paper';
import { ColorScheme, getTheme, THEME_STORAGE_KEY } from './theme';

// MMKV存储实例 - 带错误处理
let storage: MMKV | null = null;
try {
  storage = new MMKV();
} catch (error) {
  console.warn(
    'MMKV initialization failed, falling back to in-memory storage:',
    error,
  );
  storage = null;
}

// 主题上下文类型
interface ThemeContextType {
  theme: any;
  colorScheme: ColorScheme;
  isDark: boolean;
  isSystemTheme: boolean;
  setColorScheme: (scheme: ColorScheme | 'system') => void;
  toggleTheme: () => void;
  spacing: any;
  borderRadius: any;
}

// 创建主题上下文
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// 主题提供者属性
interface ThemeProviderProps {
  children: ReactNode;
}

// 内存存储降级方案
const memoryStorage: { [key: string]: string } = {};

// 获取存储的主题设置
const getStoredTheme = (): ColorScheme | 'system' => {
  try {
    if (storage) {
      const stored = storage.getString(THEME_STORAGE_KEY);
      return (stored as ColorScheme | 'system') || 'system';
    }
    // 降级到内存存储
    const stored = memoryStorage[THEME_STORAGE_KEY];
    return (stored as ColorScheme | 'system') || 'system';
  } catch {
    return 'system';
  }
};

// 保存主题设置
const saveTheme = (theme: ColorScheme | 'system'): void => {
  try {
    if (storage) {
      storage.set(THEME_STORAGE_KEY, theme);
    } else {
      // 降级到内存存储
      memoryStorage[THEME_STORAGE_KEY] = theme;
    }
  } catch (error) {
    console.warn('Failed to save theme preference:', error);
  }
};

// 获取系统主题
const getSystemColorScheme = (): ColorScheme => {
  const systemScheme = Appearance.getColorScheme();
  return systemScheme === 'dark' ? 'dark' : 'light';
};

// 主题提供者组件
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [themePreference, setThemePreference] = useState<
    ColorScheme | 'system'
  >(getStoredTheme);
  const [systemColorScheme, setSystemColorScheme] =
    useState<ColorScheme>(getSystemColorScheme);

  // 计算当前主题
  const currentColorScheme: ColorScheme =
    themePreference === 'system' ? systemColorScheme : themePreference;
  const currentTheme = getTheme(currentColorScheme);
  const isSystemTheme = themePreference === 'system';
  const isDark = currentColorScheme === 'dark';

  // 监听系统主题变化
  useEffect(() => {
    const subscription = Appearance.addChangeListener(({ colorScheme }) => {
      const newSystemScheme = colorScheme === 'dark' ? 'dark' : 'light';
      setSystemColorScheme(newSystemScheme);
    });

    return () => subscription?.remove();
  }, []);

  // 设置主题
  const setColorScheme = useCallback((scheme: ColorScheme | 'system') => {
    setThemePreference(scheme);
    saveTheme(scheme);
  }, []);

  // 切换主题
  const toggleTheme = useCallback(() => {
    if (isSystemTheme) {
      // 如果当前是系统主题，切换到相反的固定主题
      const oppositeScheme: ColorScheme =
        systemColorScheme === 'dark' ? 'light' : 'dark';
      setColorScheme(oppositeScheme);
    } else {
      // 如果是固定主题，切换到相反的固定主题
      const oppositeScheme: ColorScheme =
        currentColorScheme === 'dark' ? 'light' : 'dark';
      setColorScheme(oppositeScheme);
    }
  }, [isSystemTheme, systemColorScheme, currentColorScheme, setColorScheme]);

  const contextValue: ThemeContextType = useMemo(
    () => ({
      theme: currentTheme,
      colorScheme: currentColorScheme,
      isDark,
      isSystemTheme,
      setColorScheme,
      toggleTheme,
      spacing: currentTheme.spacing,
      borderRadius: currentTheme.borderRadius,
    }),
    [
      currentTheme,
      currentColorScheme,
      isDark,
      isSystemTheme,
      setColorScheme,
      toggleTheme,
    ],
  );

  return (
    <ThemeContext.Provider value={contextValue}>
      <PaperProvider theme={currentTheme}>{children}</PaperProvider>
    </ThemeContext.Provider>
  );
};

// 主题Hook
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// 颜色Hook - 快速访问主题颜色
export const useColors = () => {
  const { theme } = useTheme();
  return theme.colors;
};

// 间距Hook - 快速访问间距
export const useSpacing = () => {
  const { theme } = useTheme();
  return theme.spacing;
};

// 字体Hook - 快速访问字体样式
export const useTypography = () => {
  const { theme } = useTheme();
  return theme.typography;
};

// 阴影Hook - 快速访问阴影样式
export const useShadows = () => {
  const { theme } = useTheme();
  return theme.shadows;
};

// 边框半径Hook - 快速访问边框半径
export const useBorderRadius = () => {
  const { theme } = useTheme();
  return theme.borderRadius;
};

// 自定义样式Hook - 访问电商特定样式
export const useCustomStyles = () => {
  const { theme } = useTheme();
  return theme.custom;
};

// 可访问性功能已移除

// 主题状态Hook - 用于主题切换UI
export const useThemeState = () => {
  const { colorScheme, isDark, isSystemTheme, setColorScheme, toggleTheme } =
    useTheme();

  return {
    colorScheme,
    isDark,
    isSystemTheme,
    setColorScheme,
    toggleTheme,
    // 主题选项
    themeOptions: [
      { key: 'light', label: '浅色主题', icon: 'weather-sunny' },
      { key: 'dark', label: '深色主题', icon: 'weather-night' },
      { key: 'system', label: '跟随系统', icon: 'theme-light-dark' },
    ] as const,
  };
};
