/**
 * 间距系统 - 基于8px网格系统
 * 提供一致的间距规范
 */

// 基础间距单位 (8px)
const BASE_UNIT = 8;

// 间距比例系统
export const spacing = {
  // 基础间距
  xs: BASE_UNIT * 0.5, // 4px
  sm: BASE_UNIT * 1, // 8px
  md: BASE_UNIT * 2, // 16px
  lg: BASE_UNIT * 3, // 24px
  xl: BASE_UNIT * 4, // 32px
  xxl: BASE_UNIT * 6, // 48px
  xxxl: BASE_UNIT * 8, // 64px

  // 语义化间距
  none: 0,
  tiny: BASE_UNIT * 0.25, // 2px
  small: BASE_UNIT * 0.5, // 4px
  medium: BASE_UNIT * 2, // 16px
  large: BASE_UNIT * 4, // 32px
  huge: BASE_UNIT * 8, // 64px

  // 组件特定间距
  cardPadding: BASE_UNIT * 2, // 16px
  screenPadding: BASE_UNIT * 2, // 16px
  sectionSpacing: BASE_UNIT * 3, // 24px
  itemSpacing: BASE_UNIT * 1.5, // 12px
  buttonPadding: BASE_UNIT * 2, // 16px
  inputPadding: BASE_UNIT * 1.5, // 12px

  // 电商特定间距
  productCardPadding: BASE_UNIT * 1.5, // 12px
  productImageMargin: BASE_UNIT * 1, // 8px
  priceSpacing: BASE_UNIT * 0.5, // 4px
  ratingSpacing: BASE_UNIT * 0.5, // 4px
  cartItemSpacing: BASE_UNIT * 2, // 16px
  checkoutSpacing: BASE_UNIT * 3, // 24px
} as const;

// 边框半径系统
export const borderRadius = {
  none: 0,
  xs: 2,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  xxl: 24,
  full: 9999,

  // 组件特定圆角
  button: 8,
  card: 12,
  input: 8,
  modal: 16,
  avatar: 9999,
  badge: 12,

  // 电商特定圆角
  productCard: 12,
  productImage: 8,
  categoryCard: 16,
  liveCard: 12,
} as const;

// 阴影系统
export const shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 16 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 12,
  },

  // 组件特定阴影
  card: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  button: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  modal: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },

  // 电商特定阴影
  productCard: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  floatingButton: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
} as const;

// 边框宽度
export const borderWidth = {
  none: 0,
  thin: 0.5,
  normal: 1,
  thick: 2,
  thicker: 3,
} as const;

// Z-index层级
export const zIndex = {
  base: 0,
  raised: 1,
  overlay: 10,
  modal: 100,
  popover: 200,
  tooltip: 300,
  notification: 400,
  maximum: 999,
} as const;

// 透明度系统
export const opacity = {
  invisible: 0,
  faint: 0.05,
  light: 0.1,
  soft: 0.2,
  medium: 0.4,
  strong: 0.6,
  heavy: 0.8,
  opaque: 1,
} as const;

// 动画持续时间
export const duration = {
  instant: 0,
  fast: 150,
  normal: 300,
  slow: 500,
  slower: 750,
  slowest: 1000,
} as const;

// 动画缓动函数
export const easing = {
  linear: 'linear',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',

  // Material Design 缓动
  standard: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
  decelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
  accelerate: 'cubic-bezier(0.4, 0.0, 1, 1)',
  sharp: 'cubic-bezier(0.4, 0.0, 0.6, 1)',
} as const;

// 响应式断点
export const breakpoints = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
} as const;

export type SpacingKey = keyof typeof spacing;
export type BorderRadiusKey = keyof typeof borderRadius;
export type ShadowKey = keyof typeof shadows;
