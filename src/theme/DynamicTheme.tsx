/**
 * 动态主题适配 - 基于图片主色调生成主题
 * 使用react-native-image-colors提取图片颜色
 */

import React, { useCallback, useEffect, useState } from 'react';
import { getColors } from 'react-native-image-colors';
import { useTheme } from './ThemeContext';

// 动态主题配置
interface DynamicThemeConfig {
  imageUri: string;
  fallbackColor?: string;
  saturation?: number;
  brightness?: number;
}

// 颜色工具函数
class ColorUtils {
  // 将十六进制颜色转换为RGB
  static hexToRgb(hex: string): { r: number; g: number; b: number } | null {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  }

  // 将RGB转换为十六进制
  static rgbToHex(r: number, g: number, b: number): string {
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
  }

  // 调整颜色亮度
  static adjustBrightness(hex: string, factor: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const adjust = (value: number) =>
      Math.min(255, Math.max(0, Math.round(value * factor)));

    return this.rgbToHex(adjust(rgb.r), adjust(rgb.g), adjust(rgb.b));
  }

  // RGB转HSL
  private static rgbToHsl(
    r: number,
    g: number,
    b: number,
  ): { h: number; s: number; l: number } {
    r /= 255;
    g /= 255;
    b /= 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const diff = max - min;
    const l = (max + min) / 2;

    let s = 0;
    let h = 0;

    if (diff !== 0) {
      s = l > 0.5 ? diff / (2 - max - min) : diff / (max + min);

      switch (max) {
        case r:
          h = (g - b) / diff + (g < b ? 6 : 0);
          break;
        case g:
          h = (b - r) / diff + 2;
          break;
        case b:
          h = (r - g) / diff + 4;
          break;
      }
      h /= 6;
    }

    return { h, s, l };
  }

  // HSL转RGB
  private static hslToRgb(
    h: number,
    s: number,
    l: number,
  ): { r: number; g: number; b: number } {
    const hue2rgb = (p: number, q: number, t: number) => {
      if (t < 0) t += 1;
      if (t > 1) t -= 1;
      if (t < 1 / 6) return p + (q - p) * 6 * t;
      if (t < 1 / 2) return q;
      if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
      return p;
    };

    let r, g, b;

    if (s === 0) {
      r = g = b = l;
    } else {
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      r = hue2rgb(p, q, h + 1 / 3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1 / 3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255),
    };
  }

  // 调整颜色饱和度
  static adjustSaturation(hex: string, factor: number): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return hex;

    const hsl = this.rgbToHsl(rgb.r, rgb.g, rgb.b);
    hsl.s = Math.min(1, Math.max(0, hsl.s * factor));

    const newRgb = this.hslToRgb(hsl.h, hsl.s, hsl.l);
    return this.rgbToHex(newRgb.r, newRgb.g, newRgb.b);
  }

  // 获取对比色
  static getContrastColor(hex: string): string {
    const rgb = this.hexToRgb(hex);
    if (!rgb) return '#000000';

    // 计算亮度
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#FFFFFF';
  }

  // 生成颜色变体
  static generateColorVariants(baseColor: string) {
    return {
      primary: baseColor,
      primaryLight: this.adjustBrightness(baseColor, 1.2),
      primaryDark: this.adjustBrightness(baseColor, 0.8),
      primaryContainer: this.adjustBrightness(baseColor, 1.4),
      onPrimary: this.getContrastColor(baseColor),
      onPrimaryContainer: this.getContrastColor(
        this.adjustBrightness(baseColor, 1.4),
      ),
    };
  }
}

// 颜色提取结果类型
interface ColorExtractionResult {
  platform: 'android' | 'ios' | 'web';
  dominant?: string;
  primary?: string;
}

// 提取主色调的辅助函数
const extractPrimaryColor = (
  colors: ColorExtractionResult,
  fallbackColor: string,
): string => {
  if (colors.platform === 'android') {
    return colors.dominant || fallbackColor;
  }
  if (colors.platform === 'ios') {
    return colors.primary || fallbackColor;
  }
  return fallbackColor;
};

// 应用颜色调整的辅助函数
const applyColorAdjustments = (
  color: string,
  config: DynamicThemeConfig,
): string => {
  let adjustedColor = color;

  if (config.saturation) {
    adjustedColor = ColorUtils.adjustSaturation(
      adjustedColor,
      config.saturation,
    );
  }
  if (config.brightness) {
    adjustedColor = ColorUtils.adjustBrightness(
      adjustedColor,
      config.brightness,
    );
  }

  return adjustedColor;
};

// 动态主题Hook
export const useDynamicTheme = () => {
  const { theme } = useTheme();
  const [dynamicColors, setDynamicColors] = useState<Record<
    string,
    string
  > | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // 从图片提取颜色并生成主题
  const generateThemeFromImage = useCallback(
    async (config: DynamicThemeConfig) => {
      setIsLoading(true);

      try {
        const colors = await getColors(config.imageUri, {
          fallback: config.fallbackColor || '#1976D2',
          cache: true,
          key: config.imageUri,
        });

        const fallbackColor = config.fallbackColor || '#1976D2';
        const primaryColor = extractPrimaryColor(colors, fallbackColor);
        const adjustedColor = applyColorAdjustments(primaryColor, config);
        const colorVariants = ColorUtils.generateColorVariants(adjustedColor);

        const dynamicThemeColors = {
          ...theme.colors,
          primary: colorVariants.primary,
          onPrimary: colorVariants.onPrimary,
          primaryContainer: colorVariants.primaryContainer,
          onPrimaryContainer: colorVariants.onPrimaryContainer,
        };

        setDynamicColors(dynamicThemeColors);
      } catch (error) {
        console.warn('Failed to generate dynamic theme:', error);
        setDynamicColors(null);
      } finally {
        setIsLoading(false);
      }
    },
    [theme.colors],
  );

  // 重置为默认主题
  const resetTheme = useCallback(() => {
    setDynamicColors(null);
  }, []);

  // 获取当前主题（动态或默认）
  const getCurrentTheme = useCallback(() => {
    if (dynamicColors) {
      return {
        ...theme,
        colors: dynamicColors,
      };
    }
    return theme;
  }, [theme, dynamicColors]);

  return {
    generateThemeFromImage,
    resetTheme,
    getCurrentTheme,
    isDynamicTheme: !!dynamicColors,
    isLoading,
    dynamicColors,
  };
};

// 动态主题组件
interface DynamicThemeProviderProps {
  children: React.ReactNode;
  imageUri?: string;
  fallbackColor?: string;
  saturation?: number;
  brightness?: number;
  autoGenerate?: boolean;
}

export const DynamicThemeProvider: React.FC<DynamicThemeProviderProps> = ({
  children,
  imageUri,
  fallbackColor,
  saturation = 1.0,
  brightness = 1.0,
  autoGenerate = false,
}) => {
  const { generateThemeFromImage } = useDynamicTheme();

  useEffect(() => {
    if (autoGenerate && imageUri) {
      generateThemeFromImage({
        imageUri,
        fallbackColor,
        saturation,
        brightness,
      });
    }
  }, [
    imageUri,
    fallbackColor,
    saturation,
    brightness,
    autoGenerate,
    generateThemeFromImage,
  ]);

  return <>{children}</>;
};

// 图片主题预览组件
interface ImageThemePreviewProps {
  imageUri: string;
  onThemeGenerated?: (colors: Record<string, string>) => void;
  fallbackColor?: string;
}

export const ImageThemePreview: React.FC<ImageThemePreviewProps> = ({
  imageUri,
  onThemeGenerated,
  fallbackColor = '#1976D2',
}) => {
  useEffect(() => {
    const generatePreview = async () => {
      try {
        const colors = await getColors(imageUri, {
          fallback: fallbackColor,
          cache: true,
          key: imageUri,
        });

        const primaryColor = extractPrimaryColor(
          colors as ColorExtractionResult,
          fallbackColor,
        );
        const colorVariants = ColorUtils.generateColorVariants(primaryColor);
        onThemeGenerated?.(colorVariants);
      } catch (error) {
        console.warn('Failed to generate preview:', error);
      }
    };

    if (imageUri) {
      generatePreview();
    }
  }, [imageUri, fallbackColor, onThemeGenerated]);

  return null; // 这是一个逻辑组件，不渲染UI
};

export { ColorUtils };
