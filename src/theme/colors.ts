/**
 * 颜色系统 - 基于Material Design 3规范
 * 支持亮色和暗色主题
 */

// 品牌色彩
export const brandColors = {
  primary: '#1976D2', // 主品牌色 - 蓝色
  primaryVariant: '#1565C0',
  secondary: '#FF6B35', // 辅助色 - 橙色
  secondaryVariant: '#E55722',
  accent: '#4CAF50', // 强调色 - 绿色
  accentVariant: '#388E3C',
} as const;

// 语义化颜色
export const semanticColors = {
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
} as const;

// 亮色主题颜色
export const lightColors = {
  // 主要颜色
  primary: brandColors.primary,
  onPrimary: '#FFFFFF',
  primaryContainer: '#E3F2FD',
  onPrimaryContainer: '#0D47A1',

  // 次要颜色
  secondary: brandColors.secondary,
  onSecondary: '#FFFFFF',
  secondaryContainer: '#FFE0B2',
  onSecondaryContainer: '#BF360C',

  // 强调色
  tertiary: brandColors.accent,
  onTertiary: '#FFFFFF',
  tertiaryContainer: '#E8F5E8',
  onTertiaryContainer: '#1B5E20',

  // 背景色
  background: '#FEFEFE',
  onBackground: '#1C1B1F',
  surface: '#FFFFFF',
  onSurface: '#1C1B1F',
  surfaceVariant: '#F5F5F5',
  onSurfaceVariant: '#49454F',

  // 轮廓色
  outline: '#79747E',
  outlineVariant: '#CAC4D0',

  // 语义化颜色
  ...semanticColors,
  onError: '#FFFFFF',
  errorContainer: '#FFEBEE',
  onErrorContainer: '#B71C1C',

  // 阴影和覆盖
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#313033',
  inverseOnSurface: '#F4EFF4',
  inversePrimary: '#90CAF9',

  // 电商特定颜色
  price: '#E91E63', // 价格红色
  discount: '#4CAF50', // 折扣绿色
  rating: '#FFC107', // 评分黄色
  cart: '#FF6B35', // 购物车橙色
  live: '#E91E63', // 直播红色
} as const;

// 暗色主题颜色
export const darkColors = {
  // 主要颜色
  primary: '#90CAF9',
  onPrimary: '#003258',
  primaryContainer: '#004881',
  onPrimaryContainer: '#C8E6FF',

  // 次要颜色
  secondary: '#FFAB91',
  onSecondary: '#5D1A00',
  secondaryContainer: '#8A2E00',
  onSecondaryContainer: '#FFDBCF',

  // 强调色
  tertiary: '#A5D6A7',
  onTertiary: '#003A00',
  tertiaryContainer: '#005200',
  onTertiaryContainer: '#C8E6C9',

  // 背景色
  background: '#10131C',
  onBackground: '#E6E1E5',
  surface: '#1C1B1F',
  onSurface: '#E6E1E5',
  surfaceVariant: '#2B2930',
  onSurfaceVariant: '#CAC4D0',

  // 轮廓色
  outline: '#938F99',
  outlineVariant: '#49454F',

  // 语义化颜色
  success: '#81C784',
  warning: '#FFB74D',
  error: '#EF5350',
  info: '#64B5F6',
  onError: '#690005',
  errorContainer: '#93000A',
  onErrorContainer: '#FFDAD6',

  // 阴影和覆盖
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#E6E1E5',
  inverseOnSurface: '#313033',
  inversePrimary: '#1976D2',

  // 电商特定颜色
  price: '#F48FB1',
  discount: '#81C784',
  rating: '#FFD54F',
  cart: '#FFAB91',
  live: '#F48FB1',
} as const;

// 颜色透明度变体
export const createColorWithOpacity = (
  color: string,
  opacity: number,
): string => {
  const alpha = Math.round(opacity * 255)
    .toString(16)
    .padStart(2, '0');
  return `${color}${alpha}`;
};

// 渐变色定义
export const gradients = {
  primary: ['#1976D2', '#1565C0'],
  secondary: ['#FF6B35', '#E55722'],
  accent: ['#4CAF50', '#388E3C'],
  sunset: ['#FF6B35', '#FF8A65', '#FFAB91'],
  ocean: ['#1976D2', '#42A5F5', '#90CAF9'],
  forest: ['#4CAF50', '#66BB6A', '#81C784'],
} as const;

export type ColorScheme = 'light' | 'dark';
export type ThemeColors = typeof lightColors;
