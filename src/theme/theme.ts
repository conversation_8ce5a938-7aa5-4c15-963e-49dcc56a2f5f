/**
 * 主题系统 - 整合所有设计令牌
 * 基于Material Design 3规范
 */

import {
  MD3DarkTheme,
  MD3LightTheme,
  configureFonts,
} from 'react-native-paper';
import { ColorScheme, darkColors, lightColors } from './colors';
import { borderRadius, shadows, spacing } from './spacing';
import { fontFamilies, typography } from './typography';

// 字体配置
const fontConfig = {
  displayLarge: typography.displayLarge,
  displayMedium: typography.displayMedium,
  displaySmall: typography.displaySmall,
  headlineLarge: typography.headlineLarge,
  headlineMedium: typography.headlineMedium,
  headlineSmall: typography.headlineSmall,
  titleLarge: typography.titleLarge,
  titleMedium: typography.titleMedium,
  titleSmall: typography.titleSmall,
  bodyLarge: typography.bodyLarge,
  bodyMedium: typography.bodyMedium,
  bodySmall: typography.bodySmall,
  labelLarge: typography.labelLarge,
  labelMedium: typography.labelMedium,
  labelSmall: typography.labelSmall,
};

// 亮色主题
export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...lightColors,
  },
  fonts: configureFonts({ config: fontConfig }),
  spacing,
  borderRadius,
  shadows,
  typography,
  // 自定义属性
  custom: {
    fontFamilies,
    gradients: {
      primary: ['#1976D2', '#1565C0'],
      secondary: ['#FF6B35', '#E55722'],
      accent: ['#4CAF50', '#388E3C'],
    },
    animations: {
      duration: {
        short: 150,
        medium: 300,
        long: 500,
      },
      easing: {
        standard: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
        decelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
        accelerate: 'cubic-bezier(0.4, 0.0, 1, 1)',
      },
    },
    // 电商特定样式
    ecommerce: {
      productCard: {
        borderRadius: borderRadius.productCard,
        shadow: shadows.productCard,
        padding: spacing.productCardPadding,
      },
      priceText: {
        ...typography.price,
        color: lightColors.price,
      },
      discountBadge: {
        backgroundColor: lightColors.discount,
        borderRadius: borderRadius.badge,
        padding: spacing.xs,
      },
      ratingStars: {
        color: lightColors.rating,
        size: 16,
      },
      liveIndicator: {
        backgroundColor: lightColors.live,
        borderRadius: borderRadius.full,
      },
    },
  },
};

// 暗色主题
export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    ...darkColors,
  },
  fonts: configureFonts({ config: fontConfig }),
  spacing,
  borderRadius,
  shadows,
  typography,
  // 自定义属性
  custom: {
    fontFamilies,
    gradients: {
      primary: ['#90CAF9', '#64B5F6'],
      secondary: ['#FFAB91', '#FF8A65'],
      accent: ['#A5D6A7', '#81C784'],
    },
    animations: {
      duration: {
        short: 150,
        medium: 300,
        long: 500,
      },
      easing: {
        standard: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
        decelerate: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
        accelerate: 'cubic-bezier(0.4, 0.0, 1, 1)',
      },
    },
    // 电商特定样式
    ecommerce: {
      productCard: {
        borderRadius: borderRadius.productCard,
        shadow: shadows.productCard,
        padding: spacing.productCardPadding,
      },
      priceText: {
        ...typography.price,
        color: darkColors.price,
      },
      discountBadge: {
        backgroundColor: darkColors.discount,
        borderRadius: borderRadius.badge,
        padding: spacing.xs,
      },
      ratingStars: {
        color: darkColors.rating,
        size: 16,
      },
      liveIndicator: {
        backgroundColor: darkColors.live,
        borderRadius: borderRadius.full,
      },
    },
  },
};

// 主题类型定义
export type Theme = typeof lightTheme;
export type ThemeColors = Theme['colors'];
export type ThemeSpacing = Theme['spacing'];
export type ThemeBorderRadius = Theme['borderRadius'];
export type ThemeShadows = Theme['shadows'];
export type ThemeTypography = Theme['typography'];

// 主题工具函数
export const getTheme = (colorScheme: ColorScheme) =>
  colorScheme === 'dark' ? darkTheme : lightTheme;

// 主题常量
export const THEME_STORAGE_KEY = 'app_theme';
export const SYSTEM_THEME_KEY = 'system';

// 导出默认主题
export { lightTheme as defaultTheme };
export type { ColorScheme };
