/**
 * 字体系统 - 基于Material Design 3规范
 * 支持中文字体优化
 */

import { Platform } from 'react-native';

// 字体族定义
export const fontFamilies = {
  // 系统字体
  system: Platform.select({
    ios: 'System',
    android: 'Roboto',
    default: 'System',
  }),

  // 中文字体优化
  chinese: Platform.select({
    ios: 'PingFang SC',
    android: 'Noto Sans CJK SC',
    default: 'System',
  }),

  // 数字字体（等宽）
  mono: Platform.select({
    ios: 'SF Mono',
    android: 'Roboto Mono',
    default: 'monospace',
  }),
} as const;

// 字体权重
export const fontWeights = {
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semiBold: '600' as const,
  bold: '700' as const,
  extraBold: '800' as const,
};

// 行高比例
export const lineHeights = {
  tight: 1.2,
  normal: 1.4,
  relaxed: 1.6,
  loose: 1.8,
} as const;

// Material Design 3 字体规范
export const typography = {
  // Display - 大标题
  displayLarge: {
    fontFamily: fontFamilies.chinese,
    fontSize: 57,
    fontWeight: fontWeights.regular,
    lineHeight: 64,
    letterSpacing: -0.25,
  },
  displayMedium: {
    fontFamily: fontFamilies.chinese,
    fontSize: 45,
    fontWeight: fontWeights.regular,
    lineHeight: 52,
    letterSpacing: 0,
  },
  displaySmall: {
    fontFamily: fontFamilies.chinese,
    fontSize: 36,
    fontWeight: fontWeights.regular,
    lineHeight: 44,
    letterSpacing: 0,
  },

  // Headline - 标题
  headlineLarge: {
    fontFamily: fontFamilies.chinese,
    fontSize: 32,
    fontWeight: fontWeights.regular,
    lineHeight: 40,
    letterSpacing: 0,
  },
  headlineMedium: {
    fontFamily: fontFamilies.chinese,
    fontSize: 28,
    fontWeight: fontWeights.regular,
    lineHeight: 36,
    letterSpacing: 0,
  },
  headlineSmall: {
    fontFamily: fontFamilies.chinese,
    fontSize: 24,
    fontWeight: fontWeights.regular,
    lineHeight: 32,
    letterSpacing: 0,
  },

  // Title - 副标题
  titleLarge: {
    fontFamily: fontFamilies.chinese,
    fontSize: 22,
    fontWeight: fontWeights.regular,
    lineHeight: 28,
    letterSpacing: 0,
  },
  titleMedium: {
    fontFamily: fontFamilies.chinese,
    fontSize: 16,
    fontWeight: fontWeights.medium,
    lineHeight: 24,
    letterSpacing: 0.15,
  },
  titleSmall: {
    fontFamily: fontFamilies.chinese,
    fontSize: 14,
    fontWeight: fontWeights.medium,
    lineHeight: 20,
    letterSpacing: 0.1,
  },

  // Body - 正文
  bodyLarge: {
    fontFamily: fontFamilies.chinese,
    fontSize: 16,
    fontWeight: fontWeights.regular,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  bodyMedium: {
    fontFamily: fontFamilies.chinese,
    fontSize: 14,
    fontWeight: fontWeights.regular,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  bodySmall: {
    fontFamily: fontFamilies.chinese,
    fontSize: 12,
    fontWeight: fontWeights.regular,
    lineHeight: 16,
    letterSpacing: 0.4,
  },

  // Label - 标签
  labelLarge: {
    fontFamily: fontFamilies.chinese,
    fontSize: 14,
    fontWeight: fontWeights.medium,
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  labelMedium: {
    fontFamily: fontFamilies.chinese,
    fontSize: 12,
    fontWeight: fontWeights.medium,
    lineHeight: 16,
    letterSpacing: 0.5,
  },
  labelSmall: {
    fontFamily: fontFamilies.chinese,
    fontSize: 11,
    fontWeight: fontWeights.medium,
    lineHeight: 16,
    letterSpacing: 0.5,
  },

  // 电商特定字体样式
  price: {
    fontFamily: fontFamilies.mono,
    fontSize: 18,
    fontWeight: fontWeights.bold,
    lineHeight: 24,
    letterSpacing: 0,
  },
  priceSmall: {
    fontFamily: fontFamilies.mono,
    fontSize: 14,
    fontWeight: fontWeights.semiBold,
    lineHeight: 20,
    letterSpacing: 0,
  },
  badge: {
    fontFamily: fontFamilies.chinese,
    fontSize: 10,
    fontWeight: fontWeights.medium,
    lineHeight: 14,
    letterSpacing: 0.5,
  },
  button: {
    fontFamily: fontFamilies.chinese,
    fontSize: 14,
    fontWeight: fontWeights.medium,
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  caption: {
    fontFamily: fontFamilies.chinese,
    fontSize: 12,
    fontWeight: fontWeights.regular,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
} as const;

// 响应式字体大小
export const responsiveTypography = {
  // 根据屏幕尺寸调整字体大小
  getResponsiveFontSize: (baseSize: number, screenWidth: number): number => {
    const scale = screenWidth / 375; // 以iPhone X为基准
    return Math.round(baseSize * Math.min(Math.max(scale, 0.8), 1.2));
  },

  // 获取适合的行高
  getLineHeight: (
    fontSize: number,
    ratio: keyof typeof lineHeights = 'normal',
  ): number => Math.round(fontSize * lineHeights[ratio]),
};

export type TypographyVariant = keyof typeof typography;
