/**
 * 应用导航Hook
 * 提供跨导航器的导航功能
 */

import { useNavigation } from '@react-navigation/native';
import type { RootStackParamList } from '@/navigation/types';

export const useAppNavigation = () => {
  const navigation = useNavigation<any>();

  const navigateToScreen = (screenName: keyof RootStackParamList, params?: any) => {
    // 获取根导航器
    const rootNavigation = navigation.getParent?.() || navigation;
    rootNavigation.navigate(screenName, params);
  };

  const navigateToAuth = () => {
    const rootNavigation = navigation.getParent?.() || navigation;
    rootNavigation.navigate('Auth');
  };

  const navigateToMain = () => {
    const rootNavigation = navigation.getParent?.() || navigation;
    rootNavigation.navigate('Main');
  };

  return {
    navigation,
    navigateToScreen,
    navigateToAuth,
    navigateToMain,
  };
};
