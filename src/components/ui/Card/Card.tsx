/**
 * 自定义Card组件 - 基于React Native Paper扩展
 * 支持电商特定样式和可访问性
 */

import React from 'react';
import {
  ImageStyle,
  Pressable,
  StyleSheet,
  View,
  ViewStyle,
} from 'react-native';
import {
  Card as PaperCard,
  CardProps as PaperCardProps,
} from 'react-native-paper';
import { useTheme } from '../../../theme/ThemeContext';

// 卡片变体类型
export type CardVariant = 'elevated' | 'filled' | 'outlined';

// 卡片尺寸类型
export type CardSize = 'small' | 'medium' | 'large';

// 扩展的卡片属性
export interface CardProps extends Omit<PaperCardProps, 'mode'> {
  variant?: CardVariant;
  size?: CardSize;
  onPress?: () => void;
  onLongPress?: () => void;
  pressable?: boolean;
  accessibilityLabel?: string;
  children: React.ReactNode;
}

// 卡片模式映射
const getCardMode = (variant: CardVariant) => {
  const modeMap = {
    elevated: 'elevated' as const,
    filled: 'contained' as const,
    outlined: 'outlined' as const,
  };
  return modeMap[variant] || ('elevated' as const);
};

// 卡片尺寸样式映射
const getCardSizeStyles = (size: CardSize, theme: any) => {
  const sizeMap = {
    small: {
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.sm,
    },
    medium: {
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.card,
    },
    large: {
      padding: theme.spacing.lg,
      borderRadius: theme.borderRadius.lg,
    },
  };
  return sizeMap[size];
};

// 交互式卡片组件
interface InteractiveCardProps {
  onPress?: () => void;
  onLongPress?: () => void;
  accessibilityLabel?: string;
  containerStyle: any;
  variant: CardVariant;
  props: any;
  children: React.ReactNode;
}

const InteractiveCard: React.FC<InteractiveCardProps> = ({
  onPress,
  onLongPress,
  accessibilityLabel,
  containerStyle,
  variant,
  props,
  children,
}) => (
  <Pressable
    onPress={onPress}
    onLongPress={onLongPress}
    accessibilityLabel={accessibilityLabel}
    accessibilityRole='button'
    style={({ pressed }) => [containerStyle, pressed && styles.pressed]}
  >
    <PaperCard
      mode={getCardMode(variant)}
      style={styles.innerCard}
      {...(props as any)}
    >
      {children}
    </PaperCard>
  </Pressable>
);

// 卡片组件
export const Card: React.FC<CardProps> = ({
  variant = 'elevated',
  size = 'medium',
  onPress,
  onLongPress,
  pressable = false,
  style,
  children,
  accessibilityLabel,
  ...props
}) => {
  const theme = useTheme();

  const sizeStyles = getCardSizeStyles(size, theme);
  const isInteractive = pressable || onPress || onLongPress;

  const containerStyle = [
    styles.container,
    sizeStyles,
    isInteractive && styles.pressable,
    style,
  ];

  if (isInteractive) {
    return (
      <InteractiveCard
        onPress={onPress}
        onLongPress={onLongPress}
        accessibilityLabel={accessibilityLabel}
        containerStyle={containerStyle}
        variant={variant}
        props={props}
      >
        {children}
      </InteractiveCard>
    );
  }

  return (
    <PaperCard
      mode={getCardMode(variant)}
      style={containerStyle}
      {...(props as any)}
    >
      {children}
    </PaperCard>
  );
};

// 卡片内容组件
export interface CardContentProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const CardContent: React.FC<CardContentProps> = ({
  children,
  style,
}) => {
  const theme = useTheme();

  return <View style={[{ padding: theme.spacing.md }, style]}>{children}</View>;
};

// 卡片标题组件
export interface CardTitleProps {
  title: string;
  subtitle?: string;
  left?: React.ReactNode | (() => React.ReactNode);
  right?: React.ReactNode | (() => React.ReactNode);
  style?: ViewStyle;
}

// 处理左侧组件
const processLeftComponent = (
  left?: React.ReactNode | (() => React.ReactNode),
) => {
  if (!left) return undefined;
  return typeof left === 'function' ? left : () => left;
};

// 处理右侧组件
const processRightComponent = (
  right?: React.ReactNode | (() => React.ReactNode),
) => {
  if (!right) return undefined;
  return typeof right === 'function' ? right : () => right;
};

export const CardTitle: React.FC<CardTitleProps> = ({
  title,
  subtitle,
  left,
  right,
  style,
}) => {
  const leftComponent = processLeftComponent(left);
  const rightComponent = processRightComponent(right);

  return (
    <PaperCard.Title
      title={title}
      subtitle={subtitle}
      left={leftComponent}
      right={rightComponent}
      style={style}
    />
  );
};

// 卡片操作组件
export interface CardActionsProps {
  children: React.ReactNode;
  style?: ViewStyle;
}

export const CardActions: React.FC<CardActionsProps> = ({
  children,
  style,
}) => <PaperCard.Actions style={style}>{children}</PaperCard.Actions>;

// 卡片封面组件
export interface CardCoverProps {
  source: { uri: string } | number;
  style?: ImageStyle;
}

export const CardCover: React.FC<CardCoverProps> = ({ source, style }) => (
  <PaperCard.Cover source={source} style={style} />
);

// 样式定义
const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
  innerCard: {
    flex: 1,
    margin: 0,
  },
  pressable: {
    // 可交互状态的样式
  },
  pressed: {
    opacity: 0.8,
    transform: [{ scale: 0.98 }],
  },
});

// 预设卡片组件
export const ElevatedCard: React.FC<Omit<CardProps, 'variant'>> = props => (
  <Card variant='elevated' {...props} />
);

export const FilledCard: React.FC<Omit<CardProps, 'variant'>> = props => (
  <Card variant='filled' {...props} />
);

export const OutlinedCard: React.FC<Omit<CardProps, 'variant'>> = props => (
  <Card variant='outlined' {...props} />
);

export default Card;
