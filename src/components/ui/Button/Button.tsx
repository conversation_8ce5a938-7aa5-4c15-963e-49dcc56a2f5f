/**
 * 自定义Button组件 - 基于React Native Paper扩展
 * 支持电商特定样式和可访问性
 */

import React from 'react';
import { StyleSheet } from 'react-native';
import {
  Button as PaperButton,
  ButtonProps as PaperButtonProps,
} from 'react-native-paper';
import { useColors, useTheme } from '../../../theme/ThemeContext';

// 按钮变体类型
export type ButtonVariant =
  | 'primary'
  | 'secondary'
  | 'tertiary'
  | 'outline'
  | 'text'
  | 'danger'
  | 'success'
  | 'warning';

// 按钮尺寸类型
export type ButtonSize = 'small' | 'medium' | 'large';

// 扩展的按钮属性
export interface ButtonProps extends Omit<PaperButtonProps, 'mode'> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  loading?: boolean;
  leftIcon?: string;
  rightIcon?: string;
  gradient?: boolean;
  accessibilityLabel?: string;
}

// 按钮模式映射
const getButtonMode = (variant: ButtonVariant): PaperButtonProps['mode'] => {
  const modeMap: Record<ButtonVariant, PaperButtonProps['mode']> = {
    primary: 'contained',
    secondary: 'contained-tonal',
    tertiary: 'contained-tonal',
    outline: 'outlined',
    text: 'text',
    danger: 'contained',
    success: 'contained',
    warning: 'contained',
  };
  return modeMap[variant] || 'contained';
};

// 按钮颜色映射
const getButtonColors = (variant: ButtonVariant, colors: any) => {
  const colorMap: Record<ButtonVariant, string> = {
    primary: colors.primary,
    secondary: colors.secondary,
    tertiary: colors.tertiary,
    outline: colors.outline,
    text: 'transparent',
    danger: colors.error,
    success: colors.success || '#4CAF50',
    warning: colors.warning || '#FF9800',
  };
  return colorMap[variant] || colorMap.primary;
};

// 文本颜色映射
const getTextColor = (variant: ButtonVariant, colors: any) => {
  const textColorMap: Record<ButtonVariant, string> = {
    primary: colors.onPrimary,
    secondary: colors.onSecondary,
    tertiary: colors.onTertiary,
    outline: colors.primary,
    text: colors.primary,
    danger: colors.onError,
    success: '#FFFFFF',
    warning: '#FFFFFF',
  };
  return textColorMap[variant] || textColorMap.primary;
};

// 尺寸样式映射
const getSizeStyles = (size: ButtonSize, theme: any) => {
  const sizeMap = {
    small: {
      container: {
        minHeight: 32,
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xs,
      },
      text: {
        fontSize: 12,
        lineHeight: 16,
      },
    },
    medium: {
      container: {
        minHeight: 44,
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
      },
      text: {
        fontSize: 14,
        lineHeight: 20,
      },
    },
    large: {
      container: {
        minHeight: 56,
        paddingHorizontal: theme.spacing.lg,
        paddingVertical: theme.spacing.md,
      },
      text: {
        fontSize: 16,
        lineHeight: 24,
      },
    },
  };
  return sizeMap[size];
};

// 按钮组件
export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  loading = false,
  leftIcon,
  style,
  labelStyle,
  contentStyle,
  children,
  disabled,
  accessibilityLabel,
  ...props
}) => {
  const theme = useTheme();
  const colors = useColors();

  const sizeStyles = getSizeStyles(size, theme);
  const buttonColor = getButtonColors(variant, colors);
  const textColor = getTextColor(variant, colors);

  const containerStyle = [
    styles.container,
    sizeStyles.container,
    fullWidth && styles.fullWidth,
    style,
  ];

  const textStyle = [
    styles.text,
    sizeStyles.text,
    { color: textColor },
    labelStyle,
  ];

  const contentStyles = [styles.content, contentStyle];

  return (
    <PaperButton
      mode={getButtonMode(variant)}
      buttonColor={buttonColor}
      textColor={textColor}
      loading={loading}
      disabled={disabled}
      icon={leftIcon}
      style={containerStyle}
      labelStyle={textStyle}
      contentStyle={contentStyles}
      accessibilityLabel={
        accessibilityLabel ||
        (typeof children === 'string' ? children : undefined)
      }
      accessibilityRole='button'
      {...props}
    >
      {children}
    </PaperButton>
  );
};

// 样式定义
const styles = StyleSheet.create({
  container: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullWidth: {
    width: '100%',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '500',
    textAlign: 'center',
  },
});

// 预设按钮组件
export const PrimaryButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant='primary' {...props} />
);

export const SecondaryButton: React.FC<
  Omit<ButtonProps, 'variant'>
> = props => <Button variant='secondary' {...props} />;

export const OutlineButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant='outline' {...props} />
);

export const TextButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant='text' {...props} />
);

export const DangerButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant='danger' {...props} />
);

export const SuccessButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant='success' {...props} />
);

export const WarningButton: React.FC<Omit<ButtonProps, 'variant'>> = props => (
  <Button variant='warning' {...props} />
);

export default Button;
