/**
 * 主题切换组件 - 支持亮色/暗色/系统主题切换
 * 包含动画效果和可访问性支持
 */

import React, { useState } from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { Divider, IconButton, Menu, Text } from 'react-native-paper';
import {
  useColors,
  useSpacing,
  useThemeState,
} from '../../../theme/ThemeContext';

// 主题切换组件属性
export interface ThemeToggleProps {
  style?: ViewStyle;
  showLabel?: boolean;
  iconSize?: number;
  menuAnchor?: 'top' | 'bottom';
}

// 主题切换组件
export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  style,
  showLabel = false,
  iconSize = 24,
  menuAnchor = 'bottom',
}) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const { colorScheme, isDark, isSystemTheme, setColorScheme, themeOptions } =
    useThemeState();
  const colors = useColors();
  const spacing = useSpacing();
  // const { settings } = useAccessibility();

  // 获取当前主题图标
  const getCurrentThemeIcon = () => {
    if (isSystemTheme) {
      return 'theme-light-dark';
    }
    return isDark ? 'weather-night' : 'weather-sunny';
  };

  // 获取当前主题标签
  const getCurrentThemeLabel = () => {
    const currentOption = themeOptions.find(option =>
      isSystemTheme ? option.key === 'system' : option.key === colorScheme,
    );
    return currentOption?.label || '主题';
  };

  // 处理主题选择
  const handleThemeSelect = (themeKey: string) => {
    setColorScheme(themeKey as any);
    setMenuVisible(false);
  };

  // 打开菜单
  const openMenu = () => setMenuVisible(true);

  // 关闭菜单
  const closeMenu = () => setMenuVisible(false);

  return (
    <View style={[styles.container, style]}>
      <Menu
        visible={menuVisible}
        onDismiss={closeMenu}
        anchor={
          <View style={styles.anchor}>
            <IconButton
              icon={getCurrentThemeIcon()}
              size={iconSize}
              onPress={openMenu}
              accessibilityLabel={`当前主题: ${getCurrentThemeLabel()}`}
              accessibilityHint='点击切换主题'
              accessibilityRole='button'
              style={[
                styles.iconButton,
                { backgroundColor: colors.surfaceVariant },
              ]}
            />
            {showLabel && (
              <Text
                variant='labelMedium'
                style={[
                  styles.label,
                  {
                    color: colors.onSurface,
                    marginTop: spacing.xs,
                  },
                ]}
              >
                {getCurrentThemeLabel()}
              </Text>
            )}
          </View>
        }
        anchorPosition={menuAnchor}
        contentStyle={[
          styles.menuContent,
          {
            backgroundColor: colors.surface,
            borderColor: colors.outline,
          },
        ]}
      >
        <View style={styles.menuHeader}>
          <Text
            variant='titleSmall'
            style={[styles.menuTitle, { color: colors.onSurface }]}
          >
            选择主题
          </Text>
        </View>

        <Divider style={{ backgroundColor: colors.outline }} />

        {themeOptions.map((option, index) => {
          const isSelected = isSystemTheme
            ? option.key === 'system'
            : option.key === colorScheme;

          return (
            <Menu.Item
              key={option.key}
              onPress={() => handleThemeSelect(option.key)}
              title={option.label}
              leadingIcon={option.icon}
              trailingIcon={isSelected ? 'check' : undefined}
              style={[
                styles.menuItem,
                isSelected && {
                  backgroundColor: colors.primaryContainer,
                },
              ]}
              titleStyle={[
                styles.menuItemTitle,
                {
                  color: isSelected
                    ? colors.onPrimaryContainer
                    : colors.onSurface,
                  fontWeight: isSelected ? '600' : '400',
                },
              ]}
              accessibilityLabel={option.label}
              accessibilityState={{ selected: isSelected }}
            />
          );
        })}
      </Menu>
    </View>
  );
};

// 简单的主题切换按钮（只在亮色和暗色间切换）
export interface SimpleThemeToggleProps {
  style?: ViewStyle;
  iconSize?: number;
}

export const SimpleThemeToggle: React.FC<SimpleThemeToggleProps> = ({
  style,
  iconSize = 24,
}) => {
  const { isDark, toggleTheme } = useThemeState();
  const colors = useColors();

  return (
    <IconButton
      icon={isDark ? 'weather-night' : 'weather-sunny'}
      size={iconSize}
      onPress={toggleTheme}
      style={[
        styles.simpleToggle,
        {
          backgroundColor: colors.surfaceVariant,
        },
        style,
      ]}
      accessibilityLabel={`切换到${isDark ? '浅色' : '深色'}主题`}
      accessibilityRole='button'
    />
  );
};

// 样式定义
const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  anchor: {
    alignItems: 'center',
  },
  iconButton: {
    margin: 0,
  },
  label: {
    textAlign: 'center',
    fontSize: 12,
  },
  menuContent: {
    borderRadius: 12,
    borderWidth: 1,
    minWidth: 160,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  menuHeader: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  menuTitle: {
    fontWeight: '600',
  },
  menuItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 48,
  },
  menuItemTitle: {
    fontSize: 14,
  },
  simpleToggle: {
    margin: 0,
  },
});

export default ThemeToggle;
