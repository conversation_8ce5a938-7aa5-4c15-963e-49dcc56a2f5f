/**
 * 安全的手势处理器根视图
 * 兼容React Native新架构的手势处理
 */

import React, { useEffect, useState } from 'react';
import { View } from 'react-native';

interface SafeGestureHandlerRootViewProps {
  children: React.ReactNode;
  style?: any;
}

const SafeGestureHandlerRootView: React.FC<SafeGestureHandlerRootViewProps> = ({
  children,
  style = { flex: 1 },
}) => {
  const [GestureHandlerRootView, setGestureHandlerRootView] =
    useState<any>(null);
  const [isGestureHandlerAvailable, setIsGestureHandlerAvailable] =
    useState(false);

  useEffect(() => {
    // 异步检查手势处理器是否可用
    const checkGestureHandler = async () => {
      try {
        const gestureHandler = require('react-native-gesture-handler');
        if (gestureHandler && gestureHandler.GestureHandlerRootView) {
          setGestureHandlerRootView(
            () => gestureHandler.GestureHandlerRootView,
          );
          setIsGestureHandlerAvailable(true);
        } else {
          console.warn(
            'GestureHandlerRootView not found in react-native-gesture-handler',
          );
          setIsGestureHandlerAvailable(false);
        }
      } catch (error) {
        console.warn('Failed to load react-native-gesture-handler:', error);
        setIsGestureHandlerAvailable(false);
      }
    };

    checkGestureHandler();
  }, []);

  // 如果手势处理器可用，使用它
  if (isGestureHandlerAvailable && GestureHandlerRootView) {
    return React.createElement(GestureHandlerRootView, { style }, children);
  }

  // 否则降级到普通View
  return <View style={style}>{children}</View>;
};

export default SafeGestureHandlerRootView;
