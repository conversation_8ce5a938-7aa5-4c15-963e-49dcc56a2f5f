/**
 * 安全的手势处理器根视图
 * 在模拟器环境中提供降级处理
 */

import React from 'react';
import { View } from 'react-native';

interface SafeGestureHandlerRootViewProps {
  children: React.ReactNode;
  style?: any;
}

const SafeGestureHandlerRootView: React.FC<SafeGestureHandlerRootViewProps> = ({
  children,
  style = { flex: 1 },
}) => {
  // 在开发环境中，特别是在模拟器中，直接使用View
  if (__DEV__) {
    return <View style={style}>{children}</View>;
  }

  // 在生产环境中，尝试使用GestureHandlerRootView
  try {
    const { GestureHandlerRootView } = require('react-native-gesture-handler');
    return (
      <GestureHandlerRootView style={style}>{children}</GestureHandlerRootView>
    );
  } catch (error) {
    // 如果失败，降级到View
    console.warn('GestureHandler not available, using View as fallback');
    return <View style={style}>{children}</View>;
  }
};

export default SafeGestureHandlerRootView;
