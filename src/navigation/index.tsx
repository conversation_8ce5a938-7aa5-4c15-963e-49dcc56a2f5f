/**
 * 导航系统入口
 * 统一管理应用的导航配置和初始化
 */

import { NavigationContainer } from '@react-navigation/native';
import React from 'react';
import { enableScreens } from 'react-native-screens';
import { Logger } from '../config/logger';
import { useTheme } from '../theme/ThemeContext';
import { getThemeConfig, linkingConfig, performanceConfig } from './config';
import StackNavigator from './StackNavigator';

// 启用原生屏幕优化
enableScreens(performanceConfig.enableScreens);

const AppNavigator: React.FC = () => {
  const { theme, isDark } = useTheme();

  // 导航主题配置
  const navigationTheme = {
    dark: isDark,
    ...getThemeConfig(isDark),
  };

  // 导航状态变化监听
  const onStateChange = React.useCallback((state: any) => {
    if (__DEV__) {
      const currentRoute = getCurrentRoute(state);
      Logger.debug('Navigation state changed', {
        routeName: currentRoute?.name,
        params: currentRoute?.params,
      });
    }
  }, []);

  // 导航准备完成回调
  const onReady = React.useCallback(() => {
    Logger.info('Navigation container ready');
  }, []);

  return (
    <NavigationContainer
      theme={navigationTheme}
      linking={linkingConfig}
      onStateChange={onStateChange}
      onReady={onReady}
      fallback={null} // 可以添加加载组件
    >
      <StackNavigator />
    </NavigationContainer>
  );
};

// 获取当前路由信息的辅助函数
const getCurrentRoute = (state: any): any => {
  if (!state || !state.routes || state.routes.length === 0) {
    return null;
  }

  const route = state.routes[state.index];

  if (route.state) {
    return getCurrentRoute(route.state);
  }
  return route;
};

export default AppNavigator;

// 导出导航相关的类型和工具
export { linkingConfig, performanceConfig } from './config';
export * from './types';
