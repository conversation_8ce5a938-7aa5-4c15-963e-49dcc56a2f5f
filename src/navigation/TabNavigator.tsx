/**
 * Tab导航器
 * 管理底部标签页导航
 */

import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import React from 'react';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../theme/ThemeContext';
import { defaultTabOptions } from './config';
import type { MainTabParamList } from './types';

// 导入页面组件
import CartScreen from '../screens/CartScreen';
import CategoriesScreen from '../screens/CategoriesScreen';
import HomeScreen from '../screens/HomeScreen';
import ProfileScreen from '../screens/ProfileScreen';
import SocialScreen from '../screens/SocialScreen';

const Tab = createBottomTabNavigator<MainTabParamList>();

const TabNavigator: React.FC = () => {
  const { theme, isDark } = useTheme();

  // 动态主题配置
  const tabBarOptions = {
    ...defaultTabOptions,
    tabBarActiveTintColor: theme.colors.primary,
    tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
    tabBarStyle: {
      backgroundColor: theme.colors.surface,
      borderTopColor: theme.colors.outline,
      borderTopWidth: 0,
      elevation: 8,
      shadowOffset: {
        width: 0,
        height: -2,
      },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      height: 64,
      paddingBottom: 8,
      paddingTop: 8,
    },
  };

  return (
    <Tab.Navigator initialRouteName='Home' screenOptions={tabBarOptions}>
      <Tab.Screen
        name='Home'
        component={HomeScreen}
        options={{
          title: '首页',
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Icon
              name={focused ? 'home' : 'home-outline'}
              size={size}
              color={color}
            />
          ),
          tabBarBadge: undefined, // 可以动态设置徽章
        }}
      />

      <Tab.Screen
        name='Categories'
        component={CategoriesScreen}
        options={{
          title: '分类',
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Icon
              name={focused ? 'view-grid' : 'view-grid-outline'}
              size={size}
              color={color}
            />
          ),
        }}
      />

      <Tab.Screen
        name='Cart'
        component={CartScreen}
        options={{
          title: '购物车',
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Icon
              name={focused ? 'cart' : 'cart-outline'}
              size={size}
              color={color}
            />
          ),
          tabBarBadge: undefined, // 可以显示购物车商品数量
        }}
      />

      <Tab.Screen
        name='Social'
        component={SocialScreen}
        options={{
          title: '社交',
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Icon
              name={focused ? 'account-group' : 'account-group-outline'}
              size={size}
              color={color}
            />
          ),
          tabBarBadge: undefined, // 可以显示未读消息数
        }}
      />

      <Tab.Screen
        name='Profile'
        component={ProfileScreen}
        options={{
          title: '我的',
          headerShown: false,
          tabBarIcon: ({ color, size, focused }) => (
            <Icon
              name={focused ? 'account' : 'account-outline'}
              size={size}
              color={color}
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
};

export default TabNavigator;
