/**
 * 认证导航器
 * 管理用户认证流程的导航
 */

import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { useTheme } from '../theme/ThemeContext';
import { defaultStackOptions } from './config';
import type { AuthStackParamList } from './types';

// 导入认证相关页面
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import ResetPasswordScreen from '../screens/auth/ResetPasswordScreen';
import WelcomeScreen from '../screens/auth/WelcomeScreen';

const Stack = createStackNavigator<AuthStackParamList>();

const AuthNavigator: React.FC = () => {
  const { theme } = useTheme();

  // 动态主题配置
  const screenOptions = {
    ...defaultStackOptions,
    headerStyle: {
      backgroundColor: theme.colors.surface,
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 0,
    },
    headerTitleStyle: {
      fontWeight: '600' as const,
      fontSize: 18,
      color: theme.colors.onSurface,
    },
    headerTintColor: theme.colors.primary,
    cardStyle: {
      backgroundColor: theme.colors.background,
    },
  };

  return (
    <Stack.Navigator initialRouteName='Welcome' screenOptions={screenOptions}>
      <Stack.Screen
        name='Welcome'
        component={WelcomeScreen}
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
      />

      <Stack.Screen
        name='Login'
        component={LoginScreen}
        options={{
          title: '登录',
          headerBackTitleVisible: false,
        }}
      />

      <Stack.Screen
        name='Register'
        component={RegisterScreen}
        options={{
          title: '注册',
          headerBackTitleVisible: false,
        }}
      />

      <Stack.Screen
        name='ForgotPassword'
        component={ForgotPasswordScreen}
        options={{
          title: '忘记密码',
          headerBackTitleVisible: false,
        }}
      />

      <Stack.Screen
        name='ResetPassword'
        component={ResetPasswordScreen}
        options={{
          title: '重置密码',
          headerBackTitleVisible: false,
          gestureEnabled: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default AuthNavigator;
