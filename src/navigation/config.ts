/**
 * 导航配置
 * 统一管理导航的样式、动画和行为配置
 */

import type { BottomTabNavigationOptions } from '@react-navigation/bottom-tabs';
import type { DrawerNavigationOptions } from '@react-navigation/drawer';
import type { StackNavigationOptions } from '@react-navigation/stack';
import {
  CardStyleInterpolators,
  TransitionSpecs,
} from '@react-navigation/stack';
import { Platform } from 'react-native';

// 默认Stack导航配置
export const defaultStackOptions: StackNavigationOptions = {
  headerShown: true,
  gestureEnabled: true,
  animationEnabled: true,
  cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
  transitionSpec: {
    open: TransitionSpecs.TransitionIOSSpec,
    close: TransitionSpecs.TransitionIOSSpec,
  },
  headerStyle: {
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 0,
  } as any,
  headerTitleStyle: {
    fontWeight: '600',
    fontSize: 18,
  } as any,
};

// 模态页面配置
export const modalStackOptions: StackNavigationOptions = {
  ...defaultStackOptions,
  presentation: 'modal',
  cardStyleInterpolator: CardStyleInterpolators.forVerticalIOS,
  gestureDirection: 'vertical',
  gestureResponseDistance: 200,
};

// 底部Tab导航配置
export const defaultTabOptions: BottomTabNavigationOptions = {
  tabBarActiveTintColor: '#007AFF',
  tabBarInactiveTintColor: '#8E8E93',
  tabBarStyle: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 0,
    elevation: 8,
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    height: Platform.OS === 'ios' ? 84 : 64,
    paddingBottom: Platform.OS === 'ios' ? 20 : 8,
    paddingTop: 8,
  } as any,
  tabBarLabelStyle: {
    fontSize: 12,
    fontWeight: '500',
  } as any,
  tabBarIconStyle: {
    marginBottom: 2,
  } as any,
};

// 抽屉导航配置
export const defaultDrawerOptions: DrawerNavigationOptions = {
  drawerType: 'front',
  drawerPosition: 'left',
  drawerStyle: {
    backgroundColor: '#FFFFFF',
    width: 280,
  } as any,
  drawerActiveTintColor: '#007AFF',
  drawerInactiveTintColor: '#8E8E93',
  drawerLabelStyle: {
    fontSize: 16,
    fontWeight: '500',
    marginLeft: -16,
  } as any,
  drawerItemStyle: {
    marginVertical: 2,
    borderRadius: 8,
  } as any,
};

// 深度链接配置
export const linkingConfig = {
  prefixes: ['smartsocial://', 'https://smartsocial.app'],
  config: {
    screens: {
      Main: 'main',
      Auth: 'auth',
      ProductDetail: 'product/:productId',
      Search: 'search',
      Notifications: 'notifications',
      Settings: 'settings',
      WebView: 'webview',
    },
  },
} as any;

// 页面切换性能配置
export const performanceConfig = {
  // 启用原生驱动
  useNativeDriver: true,

  // 预加载相邻页面
  lazy: false,

  // 缓存策略
  freezeOnBlur: true,

  // 动画配置
  animationTypeForReplace: 'push' as const,

  // 手势配置
  gestureResponseDistance: {
    horizontal: 50,
    vertical: 135,
  },

  // 性能监控
  enableFreeze: true,
  enableScreens: true,
};

// 主题相关配置
export const getThemeConfig = (isDark: boolean) => ({
  colors: {
    primary: '#007AFF',
    background: isDark ? '#000000' : '#FFFFFF',
    card: isDark ? '#1C1C1E' : '#FFFFFF',
    text: isDark ? '#FFFFFF' : '#000000',
    border: isDark ? '#38383A' : '#E5E5EA',
    notification: '#FF3B30',
  },
});

// 无障碍配置
export const accessibilityConfig = {
  accessibilityRole: 'navigation' as const,
  accessibilityLabel: '主导航',
  accessibilityHint: '在应用页面间导航',
};
