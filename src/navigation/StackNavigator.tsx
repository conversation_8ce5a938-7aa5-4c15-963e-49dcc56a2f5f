/**
 * Stack导航器
 * 管理页面堆栈导航和模态页面
 */

import { createStackNavigator } from '@react-navigation/stack';
import React from 'react';
import { useTheme } from '../theme/ThemeContext';
import { defaultStackOptions, modalStackOptions } from './config';
import type { RootStackParamList } from './types';

// 导入导航器
import AuthNavigator from './AuthNavigator';
import DrawerNavigator from './DrawerNavigator';

// 导入页面组件
import NotificationsScreen from '../screens/NotificationsScreen';
import ProductDetailScreen from '../screens/ProductDetailScreen';
import SearchScreen from '../screens/SearchScreen';
import SettingsScreen from '../screens/SettingsScreen';
import WebViewScreen from '../screens/WebViewScreen';

const Stack = createStackNavigator<RootStackParamList>();

const StackNavigator: React.FC = () => {
  const { theme, isDark } = useTheme();

  // 动态主题配置
  const screenOptions = {
    ...defaultStackOptions,
    headerStyle: {
      backgroundColor: theme.colors.surface,
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 0,
    },
    headerTitleStyle: {
      fontWeight: '600' as const,
      fontSize: 18,
      color: theme.colors.onSurface,
    },
    headerTintColor: theme.colors.primary,
    cardStyle: {
      backgroundColor: theme.colors.background,
    },
  };

  const modalOptions = {
    ...modalStackOptions,
    headerStyle: {
      backgroundColor: theme.colors.surface,
      elevation: 0,
      shadowOpacity: 0,
      borderBottomWidth: 0,
    },
    headerTitleStyle: {
      fontWeight: '600' as const,
      fontSize: 18,
      color: theme.colors.onSurface,
    },
    headerTintColor: theme.colors.primary,
    cardStyle: {
      backgroundColor: theme.colors.background,
    },
  };

  return (
    <Stack.Navigator initialRouteName='Main' screenOptions={screenOptions}>
      {/* 主要导航 */}
      <Stack.Screen
        name='Main'
        component={DrawerNavigator}
        options={{ headerShown: false }}
      />

      {/* 认证流程 */}
      <Stack.Screen
        name='Auth'
        component={AuthNavigator}
        options={{ headerShown: false }}
      />

      {/* 模态页面组 */}
      <Stack.Group screenOptions={modalOptions}>
        <Stack.Screen
          name='ProductDetail'
          component={ProductDetailScreen}
          options={({ route }) => ({
            title: route.params?.productName || '商品详情',
            headerLeft: () => null,
            headerRight: () => null,
          })}
        />

        <Stack.Screen
          name='Search'
          component={SearchScreen}
          options={{
            title: '搜索',
          }}
        />

        <Stack.Screen
          name='Settings'
          component={SettingsScreen}
          options={{
            title: '设置',
          }}
        />

        <Stack.Screen
          name='Notifications'
          component={NotificationsScreen}
          options={{
            title: '通知',
          }}
        />
      </Stack.Group>

      {/* 全屏页面 */}
      <Stack.Screen
        name='WebView'
        component={WebViewScreen}
        options={({ route }) => ({
          title: route.params?.title || '网页',
          presentation: 'modal',
        })}
      />
    </Stack.Navigator>
  );
};

export default StackNavigator;
