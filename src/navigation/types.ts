/**
 * 导航类型定义
 * 统一管理所有导航相关的TypeScript类型
 */

import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import type { DrawerScreenProps as RNDrawerScreenProps } from '@react-navigation/drawer';
import type { NavigatorScreenParams } from '@react-navigation/native';
import type { StackScreenProps } from '@react-navigation/stack';

// 根导航参数列表
export type RootStackParamList = {
  // 主要导航
  Main: NavigatorScreenParams<DrawerParamList>;

  // 认证流程
  Auth: NavigatorScreenParams<AuthStackParamList>;

  // 模态页面
  ProductDetail: { productId: string; productName?: string };
  Search: { query?: string; category?: string };
  Settings: undefined;
  Notifications: undefined;

  // 其他页面
  WebView: { url: string; title?: string };
};

// 抽屉导航参数列表
export type DrawerParamList = {
  MainTabs: NavigatorScreenParams<MainTabParamList>;
  Orders: undefined;
  Favorites: undefined;
  Settings: undefined;
  Help: undefined;
  About: undefined;
};

// 底部Tab导航参数列表
export type MainTabParamList = {
  Home: undefined;
  Categories: undefined;
  Cart: undefined;
  Social: undefined;
  Profile: undefined;
};

// 认证流程导航参数列表
export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  ResetPassword: { token: string };
};

// 导航Props类型定义
export type RootStackScreenProps<T extends keyof RootStackParamList> =
  StackScreenProps<RootStackParamList, T>;

export type DrawerScreenProps<T extends keyof DrawerParamList> =
  RNDrawerScreenProps<DrawerParamList, T>;

export type MainTabScreenProps<T extends keyof MainTabParamList> =
  BottomTabScreenProps<MainTabParamList, T>;

export type AuthStackScreenProps<T extends keyof AuthStackParamList> =
  StackScreenProps<AuthStackParamList, T>;

// 导航配置选项
export interface NavigationOptions {
  headerShown?: boolean;
  title?: string;
  headerStyle?: {
    backgroundColor?: string;
    elevation?: number;
    shadowOpacity?: number;
  };
  headerTitleStyle?: {
    fontWeight?: string;
    fontSize?: number;
    color?: string;
  };
  headerTintColor?: string;
  gestureEnabled?: boolean;
  animationEnabled?: boolean;
  cardStyleInterpolator?: any;
  transitionSpec?: {
    open: any;
    close: any;
  };
}

// 深度链接配置
export interface LinkingConfig {
  prefixes: string[];
  config: {
    screens: Record<string, string | { path: string; exact?: boolean }>;
  };
}

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
