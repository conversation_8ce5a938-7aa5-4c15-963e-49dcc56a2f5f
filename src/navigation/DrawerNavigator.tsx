/**
 * Drawer导航器
 * 管理侧边抽屉导航
 */

import {
  createDrawerNavigator,
  DrawerContentScrollView,
  DrawerItem,
} from '@react-navigation/drawer';
import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { Avatar, Divider, Switch } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../theme/ThemeContext';
import { defaultDrawerOptions } from './config';
import type { DrawerParamList } from './types';

// 导入导航器和页面
import AboutScreen from '../screens/AboutScreen';
import FavoritesScreen from '../screens/FavoritesScreen';
import HelpScreen from '../screens/HelpScreen';
import OrdersScreen from '../screens/OrdersScreen';
import SettingsScreen from '../screens/SettingsScreen';
import TabNavigator from './TabNavigator';

const Drawer = createDrawerNavigator<DrawerParamList>();

// 自定义抽屉内容
const CustomDrawerContent: React.FC<any> = props => {
  const { theme, isDark, toggleTheme } = useTheme();

  const styles = StyleSheet.create({
    drawerContent: {
      flex: 1,
      backgroundColor: theme.colors.surface,
    },
    userSection: {
      padding: 20,
      backgroundColor: theme.colors.primaryContainer,
    },
    userInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 15,
    },
    userDetails: {
      marginLeft: 15,
      flex: 1,
    },
    userName: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.onPrimaryContainer,
    },
    userEmail: {
      fontSize: 14,
      color: theme.colors.onPrimaryContainer,
      opacity: 0.7,
      marginTop: 2,
    },
    menuSection: {
      flex: 1,
      paddingTop: 10,
    },
    themeSection: {
      padding: 20,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    themeRow: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    themeText: {
      fontSize: 16,
      color: theme.colors.onSurface,
    },
  });

  return (
    <DrawerContentScrollView {...props} style={styles.drawerContent}>
      {/* 用户信息区域 */}
      <View style={styles.userSection}>
        <View style={styles.userInfo}>
          <Avatar.Image
            size={60}
            source={{ uri: 'https://via.placeholder.com/120' }}
          />
          <View style={styles.userDetails}>
            <Text style={styles.userName}>用户名</Text>
            <Text style={styles.userEmail}><EMAIL></Text>
          </View>
        </View>
      </View>

      {/* 菜单项 */}
      <View style={styles.menuSection}>
        <DrawerItem
          label='主页'
          icon={({ color, size }) => (
            <Icon name='home-outline' color={color} size={size} />
          )}
          onPress={() => props.navigation.navigate('MainTabs')}
          activeTintColor={theme.colors.primary}
          inactiveTintColor={theme.colors.onSurfaceVariant}
        />

        <DrawerItem
          label='我的订单'
          icon={({ color, size }) => (
            <Icon name='package-variant-closed' color={color} size={size} />
          )}
          onPress={() => props.navigation.navigate('Orders')}
          activeTintColor={theme.colors.primary}
          inactiveTintColor={theme.colors.onSurfaceVariant}
        />

        <DrawerItem
          label='我的收藏'
          icon={({ color, size }) => (
            <Icon name='heart-outline' color={color} size={size} />
          )}
          onPress={() => props.navigation.navigate('Favorites')}
          activeTintColor={theme.colors.primary}
          inactiveTintColor={theme.colors.onSurfaceVariant}
        />

        <Divider style={{ marginVertical: 10 }} />

        <DrawerItem
          label='设置'
          icon={({ color, size }) => (
            <Icon name='cog-outline' color={color} size={size} />
          )}
          onPress={() => props.navigation.navigate('Settings')}
          activeTintColor={theme.colors.primary}
          inactiveTintColor={theme.colors.onSurfaceVariant}
        />

        <DrawerItem
          label='帮助与反馈'
          icon={({ color, size }) => (
            <Icon name='help-circle-outline' color={color} size={size} />
          )}
          onPress={() => props.navigation.navigate('Help')}
          activeTintColor={theme.colors.primary}
          inactiveTintColor={theme.colors.onSurfaceVariant}
        />

        <DrawerItem
          label='关于我们'
          icon={({ color, size }) => (
            <Icon name='information-outline' color={color} size={size} />
          )}
          onPress={() => props.navigation.navigate('About')}
          activeTintColor={theme.colors.primary}
          inactiveTintColor={theme.colors.onSurfaceVariant}
        />
      </View>

      {/* 主题切换 */}
      <View style={styles.themeSection}>
        <View style={styles.themeRow}>
          <Text style={styles.themeText}>深色模式</Text>
          <Switch
            value={isDark}
            onValueChange={toggleTheme}
            color={theme.colors.primary}
          />
        </View>
      </View>
    </DrawerContentScrollView>
  );
};

const DrawerNavigator: React.FC = () => {
  const { theme } = useTheme();

  // 动态主题配置
  const drawerOptions = {
    ...defaultDrawerOptions,
    drawerStyle: {
      backgroundColor: theme.colors.surface,
      width: 280,
    },
    drawerActiveTintColor: theme.colors.primary,
    drawerInactiveTintColor: theme.colors.onSurfaceVariant,
  };

  return (
    <Drawer.Navigator
      initialRouteName='MainTabs'
      drawerContent={props => <CustomDrawerContent {...props} />}
      screenOptions={drawerOptions}
    >
      <Drawer.Screen
        name='MainTabs'
        component={TabNavigator}
        options={{
          title: '智能社交电商',
          headerShown: false,
        }}
      />

      <Drawer.Screen
        name='Orders'
        component={OrdersScreen}
        options={{
          title: '我的订单',
          headerShown: true,
        }}
      />

      <Drawer.Screen
        name='Favorites'
        component={FavoritesScreen}
        options={{
          title: '我的收藏',
          headerShown: true,
        }}
      />

      <Drawer.Screen
        name='Settings'
        component={SettingsScreen}
        options={{
          title: '设置',
          headerShown: true,
        }}
      />

      <Drawer.Screen
        name='Help'
        component={HelpScreen}
        options={{
          title: '帮助与反馈',
          headerShown: true,
        }}
      />

      <Drawer.Screen
        name='About'
        component={AboutScreen}
        options={{
          title: '关于我们',
          headerShown: true,
        }}
      />
    </Drawer.Navigator>
  );
};

export default DrawerNavigator;
