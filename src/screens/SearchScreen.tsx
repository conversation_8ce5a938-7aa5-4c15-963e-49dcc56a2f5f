/**
 * 搜索页面
 * 商品搜索和筛选功能
 */

import React from 'react';
import { FlatList, ScrollView, StyleSheet, View } from 'react-native';
import { Card, Chip, Searchbar, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { RootStackScreenProps } from '../navigation/types';
import { useTheme } from '../theme/ThemeContext';

type Props = RootStackScreenProps<'Search'>;

const SearchScreen: React.FC<Props> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const [searchQuery, setSearchQuery] = React.useState(
    route.params?.query || '',
  );
  const [searchResults, setSearchResults] = React.useState([
    {
      id: 1,
      name: '智能手机',
      price: 2999,
      image: 'https://picsum.photos/150/150?random=1',
    },
    {
      id: 2,
      name: '蓝牙耳机',
      price: 299,
      image: 'https://picsum.photos/150/150?random=2',
    },
    {
      id: 3,
      name: '智能手表',
      price: 1299,
      image: 'https://picsum.photos/150/150?random=3',
    },
  ]);

  const hotSearches = ['手机', '耳机', '电脑', '服装', '化妆品'];
  const filters = ['价格', '品牌', '评分', '销量'];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    searchContainer: {
      padding: 16,
      backgroundColor: theme.colors.surface,
    },
    filtersContainer: {
      flexDirection: 'row',
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: theme.colors.surface,
    },
    filterChip: {
      marginRight: 8,
    },
    hotSearchContainer: {
      padding: 16,
    },
    hotSearchTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    hotSearchTags: {
      flexDirection: 'row',
      flexWrap: 'wrap',
    },
    hotSearchTag: {
      marginRight: 8,
      marginBottom: 8,
    },
    resultsContainer: {
      flex: 1,
      padding: 16,
    },
    resultItem: {
      flexDirection: 'row',
      marginBottom: 12,
      padding: 12,
    },
    resultImage: {
      width: 80,
      height: 80,
      borderRadius: 8,
      backgroundColor: theme.colors.surfaceVariant,
      marginRight: 12,
    },
    resultInfo: {
      flex: 1,
      justifyContent: 'space-between',
    },
    resultName: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    resultPrice: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.primary,
      marginTop: 4,
    },
  });

  const renderSearchResult = ({ item }: { item: any }) => (
    <Card
      style={styles.resultItem}
      onPress={() =>
        navigation.navigate('ProductDetail', { productId: item.id.toString() })
      }
    >
      <View style={styles.resultImage} />
      <View style={styles.resultInfo}>
        <Text style={styles.resultName}>{item.name}</Text>
        <Text style={styles.resultPrice}>¥{item.price}</Text>
      </View>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 搜索栏 */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder='搜索商品、品牌、店铺'
          onChangeText={setSearchQuery}
          value={searchQuery}
          onSubmitEditing={() => {
            // 执行搜索
          }}
          autoFocus
        />
      </View>

      {/* 筛选器 */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filtersContainer}
      >
        {filters.map(filter => (
          <Chip
            key={filter}
            style={styles.filterChip}
            onPress={() => {
              // 处理筛选
            }}
          >
            {filter}
          </Chip>
        ))}
      </ScrollView>

      {searchQuery.length === 0 ? (
        // 热门搜索
        <View style={styles.hotSearchContainer}>
          <Text style={styles.hotSearchTitle}>热门搜索</Text>
          <View style={styles.hotSearchTags}>
            {hotSearches.map(search => (
              <Chip
                key={search}
                style={styles.hotSearchTag}
                onPress={() => setSearchQuery(search)}
              >
                {search}
              </Chip>
            ))}
          </View>
        </View>
      ) : (
        // 搜索结果
        <FlatList
          data={searchResults}
          renderItem={renderSearchResult}
          keyExtractor={item => item.id.toString()}
          style={styles.resultsContainer}
          showsVerticalScrollIndicator={false}
        />
      )}
    </SafeAreaView>
  );
};

export default SearchScreen;
