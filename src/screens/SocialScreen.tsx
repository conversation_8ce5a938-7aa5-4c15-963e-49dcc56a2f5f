/**
 * 社交页面
 * 展示社交动态和互动功能
 */

import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Avatar, Button, Card, IconButton, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { MainTabScreenProps } from '../navigation/types';
import { useTheme } from '../theme/ThemeContext';

type Props = MainTabScreenProps<'Social'>;

const SocialScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();

  const socialPosts = [
    {
      id: 1,
      user: { name: '小明', avatar: 'https://picsum.photos/50/50?random=1' },
      content: '刚买的新手机真的很棒！推荐给大家',
      image: 'https://picsum.photos/400/300?random=1',
      likes: 23,
      comments: 5,
      time: '2小时前',
    },
    {
      id: 2,
      user: { name: '小红', avatar: 'https://picsum.photos/50/50?random=2' },
      content: '这个护肤品用了一个月，效果很好',
      image: 'https://picsum.photos/400/300?random=2',
      likes: 45,
      comments: 12,
      time: '4小时前',
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: 16,
      backgroundColor: theme.colors.surface,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    content: {
      flex: 1,
      padding: 16,
    },
    postCard: {
      marginBottom: 16,
      padding: 16,
    },
    postHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    userInfo: {
      flex: 1,
      marginLeft: 12,
    },
    userName: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    postTime: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      marginTop: 2,
    },
    postContent: {
      fontSize: 14,
      color: theme.colors.onSurface,
      marginBottom: 12,
      lineHeight: 20,
    },
    postImage: {
      width: '100%',
      height: 200,
      borderRadius: 8,
      marginBottom: 12,
    },
    postActions: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    actionText: {
      fontSize: 12,
      marginLeft: 4,
      color: theme.colors.onSurfaceVariant,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text variant='headlineSmall' style={{ color: theme.colors.onSurface }}>
          社交圈
        </Text>
        <IconButton
          icon='plus'
          onPress={() => {
            // 发布动态
          }}
        />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {socialPosts.map(post => (
          <Card key={post.id} style={styles.postCard}>
            <View style={styles.postHeader}>
              <Avatar.Image size={40} source={{ uri: post.user.avatar }} />
              <View style={styles.userInfo}>
                <Text style={styles.userName}>{post.user.name}</Text>
                <Text style={styles.postTime}>{post.time}</Text>
              </View>
              <IconButton icon='dots-vertical' size={20} />
            </View>

            <Text style={styles.postContent}>{post.content}</Text>

            {post.image && (
              <Card.Cover
                source={{ uri: post.image }}
                style={styles.postImage}
              />
            )}

            <View style={styles.postActions}>
              <Button
                mode='text'
                icon='heart-outline'
                compact
                onPress={() => {}}
              >
                {post.likes}
              </Button>

              <Button
                mode='text'
                icon='comment-outline'
                compact
                onPress={() => {}}
              >
                {post.comments}
              </Button>

              <Button
                mode='text'
                icon='share-outline'
                compact
                onPress={() => {}}
              >
                分享
              </Button>
            </View>
          </Card>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default SocialScreen;
