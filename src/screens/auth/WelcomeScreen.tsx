/**
 * 欢迎页面
 */

import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Button, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { AuthStackScreenProps } from '../../navigation/types';
import { useTheme } from '../../theme/ThemeContext';

type Props = AuthStackScreenProps<'Welcome'>;

const WelcomeScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    title: {
      fontSize: 32,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
      marginBottom: 16,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 48,
      textAlign: 'center',
    },
    buttonContainer: {
      width: '100%',
      gap: 12,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>智能社交电商</Text>
      <Text style={styles.subtitle}>发现更好的购物体验</Text>

      <View style={styles.buttonContainer}>
        <Button mode='contained' onPress={() => navigation.navigate('Login')}>
          登录
        </Button>
        <Button mode='outlined' onPress={() => navigation.navigate('Register')}>
          注册
        </Button>
      </View>
    </SafeAreaView>
  );
};

export default WelcomeScreen;
