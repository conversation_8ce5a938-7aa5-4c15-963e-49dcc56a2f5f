/**
 * 重置密码页面
 */

import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Button, Text, TextInput } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { AuthStackScreenProps } from '../../navigation/types';
import { useTheme } from '../../theme/ThemeContext';

type Props = AuthStackScreenProps<'ResetPassword'>;

const ResetPasswordScreen: React.FC<Props> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { token } = route.params;
  const [password, setPassword] = React.useState('');
  const [confirmPassword, setConfirmPassword] = React.useState('');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 20,
    },
    form: {
      flex: 1,
      justifyContent: 'center',
    },
    description: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 24,
      textAlign: 'center',
    },
    input: {
      marginBottom: 16,
    },
    button: {
      marginTop: 16,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.form}>
        <Text style={styles.description}>请设置您的新密码</Text>

        <TextInput
          label='新密码'
          value={password}
          onChangeText={setPassword}
          style={styles.input}
          secureTextEntry
        />
        <TextInput
          label='确认新密码'
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          style={styles.input}
          secureTextEntry
        />

        <Button
          mode='contained'
          onPress={() => {
            // 处理重置密码逻辑
            navigation.navigate('Login');
          }}
          style={styles.button}
        >
          重置密码
        </Button>
      </View>
    </SafeAreaView>
  );
};

export default ResetPasswordScreen;
