/**
 * 忘记密码页面
 */

import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Button, Text, TextInput } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { AuthStackScreenProps } from '../../navigation/types';
import { useTheme } from '../../theme/ThemeContext';

type Props = AuthStackScreenProps<'ForgotPassword'>;

const ForgotPasswordScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();
  const [email, setEmail] = React.useState('');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 20,
    },
    form: {
      flex: 1,
      justifyContent: 'center',
    },
    description: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 24,
      textAlign: 'center',
    },
    input: {
      marginBottom: 16,
    },
    button: {
      marginTop: 16,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.form}>
        <Text style={styles.description}>
          请输入您的邮箱地址，我们将发送重置密码的链接给您。
        </Text>

        <TextInput
          label='邮箱'
          value={email}
          onChangeText={setEmail}
          style={styles.input}
          keyboardType='email-address'
          autoCapitalize='none'
        />

        <Button
          mode='contained'
          onPress={() => {
            // 处理发送重置邮件逻辑
            navigation.goBack();
          }}
          style={styles.button}
        >
          发送重置邮件
        </Button>
      </View>
    </SafeAreaView>
  );
};

export default ForgotPasswordScreen;
