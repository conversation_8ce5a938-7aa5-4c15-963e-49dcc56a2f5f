/**
 * 注册页面
 */

import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Button, TextInput } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppNavigation } from '../../hooks/useAppNavigation';
import type { AuthStackScreenProps } from '../../navigation/types';
import { useTheme } from '../../theme/ThemeContext';

type Props = AuthStackScreenProps<'Register'>;

const RegisterScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();
  const { navigateToMain } = useAppNavigation();
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [confirmPassword, setConfirmPassword] = React.useState('');

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      padding: 20,
    },
    form: {
      flex: 1,
      justifyContent: 'center',
    },
    input: {
      marginBottom: 16,
    },
    button: {
      marginTop: 16,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.form}>
        <TextInput
          label='邮箱'
          value={email}
          onChangeText={setEmail}
          style={styles.input}
          keyboardType='email-address'
          autoCapitalize='none'
        />
        <TextInput
          label='密码'
          value={password}
          onChangeText={setPassword}
          style={styles.input}
          secureTextEntry
        />
        <TextInput
          label='确认密码'
          value={confirmPassword}
          onChangeText={setConfirmPassword}
          style={styles.input}
          secureTextEntry
        />

        <Button
          mode='contained'
          onPress={() => {
            // 处理注册逻辑
            navigateToMain();
          }}
          style={styles.button}
        >
          注册
        </Button>
      </View>
    </SafeAreaView>
  );
};

export default RegisterScreen;
