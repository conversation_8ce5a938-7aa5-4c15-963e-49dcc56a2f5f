/**
 * 收藏页面
 */

import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import { Card, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../theme/ThemeContext';

const FavoritesScreen: React.FC = () => {
  const { theme } = useTheme();

  const favorites = [
    { id: 1, name: '智能手机', price: 2999 },
    { id: 2, name: '蓝牙耳机', price: 299 },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: 16,
    },
    favoriteCard: {
      marginBottom: 12,
      padding: 16,
    },
    favoriteName: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    favoritePrice: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.primary,
      marginTop: 4,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        {favorites.map(item => (
          <Card key={item.id} style={styles.favoriteCard}>
            <Text style={styles.favoriteName}>{item.name}</Text>
            <Text style={styles.favoritePrice}>¥{item.price}</Text>
          </Card>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default FavoritesScreen;
