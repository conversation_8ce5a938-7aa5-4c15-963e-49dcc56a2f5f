/**
 * 帮助页面
 */

import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import { List } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../theme/ThemeContext';

const HelpScreen: React.FC = () => {
  const { theme } = useTheme();

  const helpItems = [
    { title: '如何下单', description: '了解购买流程' },
    { title: '支付方式', description: '支持的支付方式' },
    { title: '退换货政策', description: '退换货相关说明' },
    { title: '联系客服', description: '获取人工帮助' },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        {helpItems.map((item, index) => (
          <List.Item
            key={index}
            title={item.title}
            description={item.description}
            left={props => <List.Icon {...props} icon='help-circle-outline' />}
            right={props => <List.Icon {...props} icon='chevron-right' />}
            onPress={() => {}}
          />
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default HelpScreen;
