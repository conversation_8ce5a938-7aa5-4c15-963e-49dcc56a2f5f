/**
 * 首页
 * 应用主页面，展示推荐商品和功能入口
 */

import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { But<PERSON>, Card, FAB, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAppNavigation } from '../hooks/useAppNavigation';
import type { MainTabScreenProps } from '../navigation/types';
import { useTheme } from '../theme/ThemeContext';

type Props = MainTabScreenProps<'Home'>;

const HomeScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();
  const { navigateToScreen } = useAppNavigation();
  const [loading, setLoading] = React.useState(true);

  // 模拟数据加载
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.surface,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
    },
    headerActions: {
      flexDirection: 'row',
      gap: 8,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    quickActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: 12,
    },
    actionCard: {
      flex: 1,
      alignItems: 'center',
      padding: 16,
    },
    actionIcon: {
      marginBottom: 8,
    },
    actionText: {
      fontSize: 12,
      textAlign: 'center',
      color: theme.colors.onSurface,
    },
    productCard: {
      marginBottom: 12,
    },
    fab: {
      position: 'absolute',
      margin: 16,
      right: 0,
      bottom: 0,
      backgroundColor: theme.colors.primary,
    },
  });

  // 骨架屏组件
  const LoadingSkeleton = () => (
    <SkeletonPlaceholder
      backgroundColor={theme.colors.surfaceVariant}
      highlightColor={theme.colors.surface}
    >
      <View style={{ padding: 16 }}>
        {/* 快捷操作骨架 */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            marginBottom: 24,
          }}
        >
          {[1, 2, 3, 4].map(item => (
            <View key={item} style={{ alignItems: 'center' }}>
              <View
                style={{
                  width: 60,
                  height: 60,
                  borderRadius: 8,
                  marginBottom: 8,
                }}
              />
              <View style={{ width: 40, height: 12, borderRadius: 4 }} />
            </View>
          ))}
        </View>

        {/* 商品列表骨架 */}
        {[1, 2, 3].map(item => (
          <View key={item} style={{ marginBottom: 16 }}>
            <View
              style={{
                width: '100%',
                height: 200,
                borderRadius: 8,
                marginBottom: 8,
              }}
            />
            <View
              style={{
                width: '80%',
                height: 16,
                borderRadius: 4,
                marginBottom: 4,
              }}
            />
            <View style={{ width: '60%', height: 14, borderRadius: 4 }} />
          </View>
        ))}
      </View>
    </SkeletonPlaceholder>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>智能社交电商</Text>
          <View style={styles.headerActions}>
            <Button
              mode='text'
              icon='magnify'
              onPress={() => navigateToScreen('Search')}
            >
              搜索
            </Button>
          </View>
        </View>
        <LoadingSkeleton />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部 */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>智能社交电商</Text>
        <View style={styles.headerActions}>
          <Button
            mode='text'
            icon='magnify'
            onPress={() => navigateToScreen('Search')}
          >
            搜索
          </Button>
          <Button
            mode='text'
            icon='bell-outline'
            onPress={() => navigateToScreen('Notifications')}
          >
            通知
          </Button>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 快捷操作 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>快捷功能</Text>
          <View style={styles.quickActions}>
            <Card
              style={styles.actionCard}
              onPress={() => navigation.navigate('Categories')}
            >
              <Icon
                name='view-grid'
                size={32}
                color={theme.colors.primary}
                style={styles.actionIcon}
              />
              <Text style={styles.actionText}>商品分类</Text>
            </Card>

            <Card
              style={styles.actionCard}
              onPress={() => navigateToScreen('Search', { query: '热销' })}
            >
              <Icon
                name='fire'
                size={32}
                color={theme.colors.primary}
                style={styles.actionIcon}
              />
              <Text style={styles.actionText}>热销商品</Text>
            </Card>

            <Card
              style={styles.actionCard}
              onPress={() => navigation.navigate('Social')}
            >
              <Icon
                name='account-group'
                size={32}
                color={theme.colors.primary}
                style={styles.actionIcon}
              />
              <Text style={styles.actionText}>社交圈</Text>
            </Card>

            <Card
              style={styles.actionCard}
              onPress={() => navigation.navigate('Cart')}
            >
              <Icon
                name='cart'
                size={32}
                color={theme.colors.primary}
                style={styles.actionIcon}
              />
              <Text style={styles.actionText}>购物车</Text>
            </Card>
          </View>
        </View>

        {/* 推荐商品 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>为您推荐</Text>

          {[1, 2, 3].map(item => (
            <Card
              key={item}
              style={styles.productCard}
              onPress={() =>
                navigateToScreen('ProductDetail', {
                  productId: `product-${item}`,
                })
              }
            >
              <Card.Cover
                source={{ uri: `https://picsum.photos/400/200?random=${item}` }}
              />
              <Card.Content style={{ paddingTop: 12 }}>
                <Text variant='titleMedium'>推荐商品 {item}</Text>
                <Text
                  variant='bodyMedium'
                  style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}
                >
                  这是一个很棒的商品，值得您拥有
                </Text>
                <Text
                  variant='titleLarge'
                  style={{ color: theme.colors.primary, marginTop: 8 }}
                >
                  ¥{(Math.random() * 1000 + 100).toFixed(2)}
                </Text>
              </Card.Content>
            </Card>
          ))}
        </View>
      </ScrollView>

      {/* 浮动操作按钮 */}
      <FAB
        icon='plus'
        style={styles.fab}
        onPress={() => {
          // 这里可以添加快速操作，比如扫码、语音搜索等
        }}
      />
    </SafeAreaView>
  );
};

export default HomeScreen;
