/**
 * 购物车页面
 * 展示购物车商品和结算功能
 */

import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { <PERSON><PERSON>, Card, IconButton, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import type { MainTabScreenProps } from '../navigation/types';
import { useTheme } from '../theme/ThemeContext';

type Props = MainTabScreenProps<'Cart'>;

const CartScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();
  const [cartItems, setCartItems] = React.useState([
    {
      id: 1,
      name: '智能手机',
      price: 2999,
      quantity: 1,
      image: 'https://picsum.photos/100/100?random=1',
    },
    {
      id: 2,
      name: '蓝牙耳机',
      price: 299,
      quantity: 2,
      image: 'https://picsum.photos/100/100?random=2',
    },
  ]);

  const updateQuantity = (id: number, change: number) => {
    setCartItems(items =>
      items
        .map(item =>
          item.id === id
            ? { ...item, quantity: Math.max(0, item.quantity + change) }
            : item,
        )
        .filter(item => item.quantity > 0),
    );
  };

  const totalAmount = cartItems.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0,
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: 16,
      backgroundColor: theme.colors.surface,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    emptyText: {
      fontSize: 18,
      color: theme.colors.onSurfaceVariant,
      marginTop: 16,
    },
    cartItem: {
      marginBottom: 12,
      padding: 16,
    },
    itemContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    itemImage: {
      width: 80,
      height: 80,
      borderRadius: 8,
      backgroundColor: theme.colors.surfaceVariant,
      marginRight: 16,
    },
    itemDetails: {
      flex: 1,
    },
    itemName: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
      marginBottom: 4,
    },
    itemPrice: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    quantityControls: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: 8,
    },
    quantityText: {
      fontSize: 16,
      marginHorizontal: 16,
      minWidth: 30,
      textAlign: 'center',
    },
    footer: {
      padding: 16,
      backgroundColor: theme.colors.surface,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    totalRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    totalText: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
    },
    totalAmount: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    checkoutButton: {
      paddingVertical: 8,
    },
  });

  if (cartItems.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text
            variant='headlineSmall'
            style={{ color: theme.colors.onSurface }}
          >
            购物车
          </Text>
        </View>
        <View style={styles.emptyContainer}>
          <Icon
            name='cart-outline'
            size={80}
            color={theme.colors.onSurfaceVariant}
          />
          <Text style={styles.emptyText}>购物车是空的</Text>
          <Button
            mode='contained'
            onPress={() => navigation.navigate('Home')}
            style={{ marginTop: 24 }}
          >
            去购物
          </Button>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text variant='headlineSmall' style={{ color: theme.colors.onSurface }}>
          购物车 ({cartItems.length})
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {cartItems.map(item => (
          <Card key={item.id} style={styles.cartItem}>
            <View style={styles.itemContent}>
              <View style={styles.itemImage} />
              <View style={styles.itemDetails}>
                <Text style={styles.itemName}>{item.name}</Text>
                <Text style={styles.itemPrice}>¥{item.price}</Text>
                <View style={styles.quantityControls}>
                  <IconButton
                    icon='minus'
                    size={20}
                    onPress={() => updateQuantity(item.id, -1)}
                  />
                  <Text style={styles.quantityText}>{item.quantity}</Text>
                  <IconButton
                    icon='plus'
                    size={20}
                    onPress={() => updateQuantity(item.id, 1)}
                  />
                </View>
              </View>
            </View>
          </Card>
        ))}
      </ScrollView>

      <View style={styles.footer}>
        <View style={styles.totalRow}>
          <Text style={styles.totalText}>总计:</Text>
          <Text style={styles.totalAmount}>¥{totalAmount.toFixed(2)}</Text>
        </View>
        <Button
          mode='contained'
          onPress={() => {
            // 这里处理结算逻辑
          }}
          style={styles.checkoutButton}
        >
          立即结算
        </Button>
      </View>
    </SafeAreaView>
  );
};

export default CartScreen;
