/**
 * 商品详情页面
 * 展示商品详细信息和购买功能
 */

import React from 'react';
import { Dimensions, ScrollView, StyleSheet, View } from 'react-native';
import { Button, Card, Chip, IconButton, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { RootStackScreenProps } from '../navigation/types';
import { useTheme } from '../theme/ThemeContext';

type Props = RootStackScreenProps<'ProductDetail'>;

const { width } = Dimensions.get('window');

const ProductDetailScreen: React.FC<Props> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { productId, productName } = route.params;
  const [quantity, setQuantity] = React.useState(1);
  const [isFavorite, setIsFavorite] = React.useState(false);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: 16,
      backgroundColor: theme.colors.surface,
    },
    productImage: {
      width: width,
      height: width * 0.8,
      backgroundColor: theme.colors.surfaceVariant,
    },
    content: {
      padding: 16,
    },
    productTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
      marginBottom: 8,
    },
    productPrice: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.primary,
      marginBottom: 16,
    },
    originalPrice: {
      fontSize: 16,
      textDecorationLine: 'line-through',
      color: theme.colors.onSurfaceVariant,
      marginLeft: 8,
    },
    tagsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: 16,
    },
    tag: {
      marginRight: 8,
      marginBottom: 8,
    },
    section: {
      marginBottom: 24,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    description: {
      fontSize: 14,
      lineHeight: 20,
      color: theme.colors.onSurface,
    },
    quantityContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    quantityLabel: {
      fontSize: 16,
      color: theme.colors.onSurface,
      marginRight: 16,
    },
    quantityControls: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    quantityText: {
      fontSize: 16,
      marginHorizontal: 16,
      minWidth: 30,
      textAlign: 'center',
    },
    footer: {
      flexDirection: 'row',
      padding: 16,
      backgroundColor: theme.colors.surface,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    addToCartButton: {
      flex: 1,
      marginRight: 8,
    },
    buyNowButton: {
      flex: 1,
      marginLeft: 8,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部 */}
      <View style={styles.header}>
        <IconButton icon='close' onPress={() => navigation.goBack()} />
        <View style={{ flexDirection: 'row' }}>
          <IconButton
            icon={isFavorite ? 'heart' : 'heart-outline'}
            iconColor={isFavorite ? theme.colors.error : theme.colors.onSurface}
            onPress={() => setIsFavorite(!isFavorite)}
          />
          <IconButton
            icon='share-outline'
            onPress={() => {
              // 分享功能
            }}
          />
        </View>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* 商品图片 */}
        <Card.Cover
          source={{ uri: `https://picsum.photos/400/320?random=${productId}` }}
          style={styles.productImage}
        />

        <View style={styles.content}>
          {/* 商品基本信息 */}
          <Text style={styles.productTitle}>
            {productName || `商品 ${productId}`}
          </Text>

          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.productPrice}>¥299.00</Text>
            <Text style={styles.originalPrice}>¥399.00</Text>
          </View>

          {/* 标签 */}
          <View style={styles.tagsContainer}>
            <Chip style={styles.tag} compact>
              包邮
            </Chip>
            <Chip style={styles.tag} compact>
              7天退换
            </Chip>
            <Chip style={styles.tag} compact>
              正品保证
            </Chip>
          </View>

          {/* 商品描述 */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>商品详情</Text>
            <Text style={styles.description}>
              这是一个优质的商品，采用先进的技术和优质的材料制作而成。
              具有出色的性能和耐用性，是您的理想选择。
              {'\n\n'}
              产品特点：
              {'\n'}• 高品质材料
              {'\n'}• 精工制作
              {'\n'}• 性能卓越
              {'\n'}• 售后保障
            </Text>
          </View>

          {/* 数量选择 */}
          <View style={styles.quantityContainer}>
            <Text style={styles.quantityLabel}>数量:</Text>
            <View style={styles.quantityControls}>
              <IconButton
                icon='minus'
                size={20}
                onPress={() => setQuantity(Math.max(1, quantity - 1))}
              />
              <Text style={styles.quantityText}>{quantity}</Text>
              <IconButton
                icon='plus'
                size={20}
                onPress={() => setQuantity(quantity + 1)}
              />
            </View>
          </View>
        </View>
      </ScrollView>

      {/* 底部操作栏 */}
      <View style={styles.footer}>
        <Button
          mode='outlined'
          onPress={() => {
            // 添加到购物车
            navigation.goBack();
          }}
          style={styles.addToCartButton}
        >
          加入购物车
        </Button>
        <Button
          mode='contained'
          onPress={() => {
            // 立即购买
            navigation.goBack();
          }}
          style={styles.buyNowButton}
        >
          立即购买
        </Button>
      </View>
    </SafeAreaView>
  );
};

export default ProductDetailScreen;
