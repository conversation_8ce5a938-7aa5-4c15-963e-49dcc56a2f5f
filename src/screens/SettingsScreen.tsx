/**
 * 设置页面
 */

import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Divider, List, Switch, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { RootStackScreenProps } from '../navigation/types';
import { useTheme } from '../theme/ThemeContext';

type Props = RootStackScreenProps<'Settings'>;

const SettingsScreen: React.FC<Props> = ({ navigation }) => {
  const { theme, isDark, toggleTheme } = useTheme();
  const [notifications, setNotifications] = React.useState(true);
  const [autoUpdate, setAutoUpdate] = React.useState(false);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
    },
    section: {
      marginBottom: 8,
    },
    sectionTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.primary,
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: theme.colors.surfaceVariant,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>外观</Text>
          <List.Item
            title='深色模式'
            description='切换应用主题'
            left={props => <List.Icon {...props} icon='theme-light-dark' />}
            right={() => <Switch value={isDark} onValueChange={toggleTheme} />}
          />
        </View>

        <Divider />

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>通知</Text>
          <List.Item
            title='推送通知'
            description='接收应用推送消息'
            left={props => <List.Icon {...props} icon='bell-outline' />}
            right={() => (
              <Switch value={notifications} onValueChange={setNotifications} />
            )}
          />
        </View>

        <Divider />

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>应用</Text>
          <List.Item
            title='自动更新'
            description='自动下载应用更新'
            left={props => <List.Icon {...props} icon='download-outline' />}
            right={() => (
              <Switch value={autoUpdate} onValueChange={setAutoUpdate} />
            )}
          />
          <List.Item
            title='清除缓存'
            description='清理应用缓存数据'
            left={props => <List.Icon {...props} icon='delete-outline' />}
            right={props => <List.Icon {...props} icon='chevron-right' />}
            onPress={() => {}}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default SettingsScreen;
