/**
 * 个人中心页面
 * 展示用户信息和个人功能
 */

import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Avatar, Button, Divider, List, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppNavigation } from '../hooks/useAppNavigation';
import type { MainTabScreenProps } from '../navigation/types';
import { useTheme } from '../theme/ThemeContext';

type Props = MainTabScreenProps<'Profile'>;

const ProfileScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();
  const { navigateToScreen, navigateToAuth } = useAppNavigation();

  const menuItems = [
    {
      title: '我的订单',
      icon: 'package-variant-closed',
      onPress: () => navigation.getParent()?.navigate('Orders'),
    },
    {
      title: '我的收藏',
      icon: 'heart-outline',
      onPress: () => navigation.getParent()?.navigate('Favorites'),
    },
    { title: '地址管理', icon: 'map-marker-outline', onPress: () => {} },
    { title: '优惠券', icon: 'ticket-outline', onPress: () => {} },
    { title: '积分商城', icon: 'star-outline', onPress: () => {} },
  ];

  const settingsItems = [
    {
      title: '设置',
      icon: 'cog-outline',
      onPress: () => navigateToScreen('Settings'),
    },
    {
      title: '帮助与反馈',
      icon: 'help-circle-outline',
      onPress: () => navigation.getParent()?.navigate('Help'),
    },
    {
      title: '关于我们',
      icon: 'information-outline',
      onPress: () => navigation.getParent()?.navigate('About'),
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: 20,
      backgroundColor: theme.colors.primaryContainer,
      alignItems: 'center',
    },
    userInfo: {
      alignItems: 'center',
      marginTop: 16,
    },
    userName: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.onPrimaryContainer,
      marginTop: 12,
    },
    userEmail: {
      fontSize: 14,
      color: theme.colors.onPrimaryContainer,
      opacity: 0.7,
      marginTop: 4,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginTop: 20,
      paddingHorizontal: 20,
    },
    statItem: {
      alignItems: 'center',
    },
    statNumber: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.onPrimaryContainer,
    },
    statLabel: {
      fontSize: 12,
      color: theme.colors.onPrimaryContainer,
      opacity: 0.7,
      marginTop: 4,
    },
    content: {
      flex: 1,
    },
    section: {
      marginTop: 8,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.onSurface,
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    menuItem: {
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    logoutButton: {
      margin: 16,
      marginTop: 24,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      {/* 用户信息头部 */}
      <View style={styles.header}>
        <View style={styles.userInfo}>
          <Avatar.Image
            size={80}
            source={{ uri: 'https://picsum.photos/160/160?random=user' }}
          />
          <Text style={styles.userName}>用户名</Text>
          <Text style={styles.userEmail}><EMAIL></Text>
        </View>

        {/* 统计信息 */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>12</Text>
            <Text style={styles.statLabel}>订单</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>36</Text>
            <Text style={styles.statLabel}>收藏</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>8</Text>
            <Text style={styles.statLabel}>关注</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>24</Text>
            <Text style={styles.statLabel}>粉丝</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* 我的服务 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>我的服务</Text>
          {menuItems.map((item, index) => (
            <List.Item
              key={index}
              title={item.title}
              left={props => <List.Icon {...props} icon={item.icon} />}
              right={props => <List.Icon {...props} icon='chevron-right' />}
              onPress={item.onPress}
              style={styles.menuItem}
            />
          ))}
        </View>

        <Divider />

        {/* 设置与帮助 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>设置与帮助</Text>
          {settingsItems.map((item, index) => (
            <List.Item
              key={index}
              title={item.title}
              left={props => <List.Icon {...props} icon={item.icon} />}
              right={props => <List.Icon {...props} icon='chevron-right' />}
              onPress={item.onPress}
              style={styles.menuItem}
            />
          ))}
        </View>

        {/* 退出登录 */}
        <Button
          mode='outlined'
          onPress={() => {
            // 处理退出登录
            navigateToAuth();
          }}
          style={styles.logoutButton}
          textColor={theme.colors.error}
        >
          退出登录
        </Button>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ProfileScreen;
