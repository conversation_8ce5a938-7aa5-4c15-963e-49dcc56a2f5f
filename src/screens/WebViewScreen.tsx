/**
 * WebView页面
 */

import React from 'react';
import { StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { RootStackScreenProps } from '../navigation/types';
import { useTheme } from '../theme/ThemeContext';

type Props = RootStackScreenProps<'WebView'>;

const WebViewScreen: React.FC<Props> = ({ navigation, route }) => {
  const { theme } = useTheme();
  const { url, title } = route.params;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <Text>WebView: {url}</Text>
      <Text>Title: {title}</Text>
    </SafeAreaView>
  );
};

export default WebViewScreen;
