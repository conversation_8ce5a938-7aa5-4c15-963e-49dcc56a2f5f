/**
 * 订单页面
 */

import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { <PERSON>ton, Card, Chip, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../theme/ThemeContext';

const OrdersScreen: React.FC = () => {
  const { theme } = useTheme();

  const orders = [
    {
      id: 1,
      status: '已发货',
      total: 299,
      items: ['智能手机'],
      date: '2024-01-15',
    },
    {
      id: 2,
      status: '待付款',
      total: 599,
      items: ['蓝牙耳机', '充电器'],
      date: '2024-01-14',
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: 16,
    },
    orderCard: {
      marginBottom: 16,
      padding: 16,
    },
    orderHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    orderId: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    orderTotal: {
      fontSize: 18,
      fontWeight: 'bold',
      color: theme.colors.primary,
      marginTop: 8,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        {orders.map(order => (
          <Card key={order.id} style={styles.orderCard}>
            <View style={styles.orderHeader}>
              <Text style={styles.orderId}>订单 #{order.id}</Text>
              <Chip>{order.status}</Chip>
            </View>
            <Text>商品: {order.items.join(', ')}</Text>
            <Text>日期: {order.date}</Text>
            <Text style={styles.orderTotal}>总计: ¥{order.total}</Text>
            <Button mode='outlined' style={{ marginTop: 12 }}>
              查看详情
            </Button>
          </Card>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default OrdersScreen;
