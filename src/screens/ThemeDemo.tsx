/**
 * 主题演示页面 - 展示主题系统的各种功能
 */

import React from 'react';
import { Image, ScrollView, StyleSheet, View } from 'react-native';
import { Chip, Divider, Surface, Text } from 'react-native-paper';
import {
  Button,
  DangerButton,
  OutlineButton,
  PrimaryButton,
  SecondaryButton,
  SuccessButton,
  TextButton,
  WarningButton,
} from '../components/ui/Button';
import {
  Card,
  CardActions,
  CardContent,
  CardTitle,
  ElevatedCard,
  FilledCard,
  OutlinedCard,
} from '../components/ui/Card';
import { SimpleThemeToggle, ThemeToggle } from '../components/ui/ThemeToggle';
import { useDynamicTheme } from '../theme/DynamicTheme';
import {
  useColors,
  useCustomStyles,
  useSpacing,
  useTypography,
} from '../theme/ThemeContext';

const ThemeDemo: React.FC = () => {
  const colors = useColors();
  const spacing = useSpacing();
  const typography = useTypography();
  const customStyles = useCustomStyles();

  const { generateThemeFromImage, resetTheme, isDynamicTheme } =
    useDynamicTheme();

  // 测试图片URL
  const testImageUrl = 'https://picsum.photos/300/200';

  // 生成动态主题
  const handleGenerateDynamicTheme = () => {
    generateThemeFromImage({
      imageUri: testImageUrl,
      fallbackColor: '#1976D2',
      saturation: 1.2,
      brightness: 1.0,
    });
  };

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: colors.background }]}
      contentContainerStyle={styles.content}
    >
      {/* 标题区域 */}
      <Surface style={[styles.header, { backgroundColor: colors.surface }]}>
        <Text variant='headlineMedium' style={{ color: colors.onSurface }}>
          主题系统演示
        </Text>
        <Text
          variant='bodyMedium'
          style={{ color: colors.onSurfaceVariant, marginTop: spacing.xs }}
        >
          展示React Native Paper + 自定义主题系统
        </Text>
      </Surface>

      {/* 主题控制区域 */}
      <Card style={styles.section}>
        <CardContent>
          <Text
            variant='titleMedium'
            style={{ color: colors.onSurface, marginBottom: spacing.md }}
          >
            主题控制
          </Text>

          <View style={styles.themeControls}>
            <View style={styles.themeControl}>
              <Text variant='bodyMedium' style={{ color: colors.onSurface }}>
                主题切换菜单
              </Text>
              <ThemeToggle showLabel />
            </View>

            <View style={styles.themeControl}>
              <Text variant='bodyMedium' style={{ color: colors.onSurface }}>
                简单切换
              </Text>
              <SimpleThemeToggle />
            </View>
          </View>

          <Divider style={{ marginVertical: spacing.md }} />

          <View style={styles.dynamicThemeSection}>
            <Text
              variant='bodyMedium'
              style={{ color: colors.onSurface, marginBottom: spacing.sm }}
            >
              动态主题 {isDynamicTheme && '(已启用)'}
            </Text>

            <Image
              source={{ uri: testImageUrl }}
              style={styles.testImage}
              resizeMode='cover'
            />

            <View style={styles.dynamicThemeButtons}>
              <Button
                variant='outline'
                size='small'
                onPress={handleGenerateDynamicTheme}
              >
                <Text>从图片生成主题</Text>
              </Button>
              <Button
                variant='text'
                size='small'
                onPress={resetTheme}
                disabled={!isDynamicTheme}
              >
                <Text>重置主题</Text>
              </Button>
            </View>
          </View>
        </CardContent>
      </Card>

      {/* 按钮演示 */}
      <Card style={styles.section}>
        <CardContent>
          <Text
            variant='titleMedium'
            style={{ color: colors.onSurface, marginBottom: spacing.md }}
          >
            按钮组件
          </Text>

          <View style={styles.buttonGrid}>
            <PrimaryButton size='small'>
              <Text>主要按钮</Text>
            </PrimaryButton>
            <SecondaryButton size='small'>
              <Text>次要按钮</Text>
            </SecondaryButton>
            <OutlineButton size='small'>
              <Text>轮廓按钮</Text>
            </OutlineButton>
            <TextButton size='small'>
              <Text>文本按钮</Text>
            </TextButton>
            <DangerButton size='small'>
              <Text>危险按钮</Text>
            </DangerButton>
            <SuccessButton size='small'>
              <Text>成功按钮</Text>
            </SuccessButton>
            <WarningButton size='small'>
              <Text>警告按钮</Text>
            </WarningButton>
          </View>

          <View style={styles.buttonSizes}>
            <PrimaryButton size='small'>
              <Text>小按钮</Text>
            </PrimaryButton>
            <PrimaryButton size='medium'>
              <Text>中按钮</Text>
            </PrimaryButton>
            <PrimaryButton size='large'>
              <Text>大按钮</Text>
            </PrimaryButton>
          </View>

          <PrimaryButton fullWidth style={{ marginTop: spacing.md }}>
            <Text>全宽按钮 评分色</Text>
          </PrimaryButton>
        </CardContent>
      </Card>

      {/* 卡片演示 */}
      <Card style={styles.section}>
        <CardContent>
          <Text
            variant='titleMedium'
            style={{ color: colors.onSurface, marginBottom: spacing.md }}
          >
            卡片组件
          </Text>

          <View style={styles.cardGrid}>
            <ElevatedCard size='small' style={styles.demoCard}>
              <CardContent>
                <Text variant='bodySmall'>悬浮卡片</Text>
              </CardContent>
            </ElevatedCard>

            <FilledCard size='small' style={styles.demoCard}>
              <CardContent>
                <Text variant='bodySmall'>填充卡片</Text>
              </CardContent>
            </FilledCard>

            <OutlinedCard size='small' style={styles.demoCard}>
              <CardContent>
                <Text variant='bodySmall'>轮廓卡片</Text>
              </CardContent>
            </OutlinedCard>
          </View>

          {/* 可交互卡片 */}
          <Card
            pressable
            onPress={() => console.log('Card pressed')}
            style={[styles.interactiveCard, { marginTop: spacing.md }]}
          >
            <CardTitle
              title='可交互卡片'
              subtitle='点击我试试'
              left={() => <Text>🎯</Text>}
            />
            <CardContent>
              <Text variant='bodyMedium' style={{ color: colors.onSurface }}>
                这是一个可以点击的卡片，支持触摸反馈和可访问性。
              </Text>
            </CardContent>
            <CardActions>
              <TextButton>
                <Text>取消</Text>
              </TextButton>
              <PrimaryButton>
                <Text>确认</Text>
              </PrimaryButton>
            </CardActions>
          </Card>
        </CardContent>
      </Card>

      {/* 颜色演示 */}
      <Card style={styles.section}>
        <CardContent>
          <Text
            variant='titleMedium'
            style={{ color: colors.onSurface, marginBottom: spacing.md }}
          >
            颜色系统
          </Text>

          <View style={styles.colorGrid}>
            <View
              style={[styles.colorSwatch, { backgroundColor: colors.primary }]}
            >
              <Text style={[styles.colorLabel, { color: colors.onPrimary }]}>
                Primary
              </Text>
            </View>
            <View
              style={[
                styles.colorSwatch,
                { backgroundColor: colors.secondary },
              ]}
            >
              <Text style={[styles.colorLabel, { color: colors.onSecondary }]}>
                Secondary
              </Text>
            </View>
            <View
              style={[styles.colorSwatch, { backgroundColor: colors.tertiary }]}
            >
              <Text style={[styles.colorLabel, { color: colors.onTertiary }]}>
                Tertiary
              </Text>
            </View>
            <View
              style={[styles.colorSwatch, { backgroundColor: colors.error }]}
            >
              <Text style={[styles.colorLabel, { color: colors.onError }]}>
                Error
              </Text>
            </View>
          </View>

          {/* 电商特定颜色 */}
          <Text
            variant='bodyMedium'
            style={{
              color: colors.onSurface,
              marginTop: spacing.md,
              marginBottom: spacing.sm,
            }}
          >
            电商特定颜色
          </Text>
          <View style={styles.ecommerceColors}>
            <Chip
              style={{ backgroundColor: colors.price || '#E91E63' }}
              textStyle={{ color: '#FFFFFF' }}
            >
              <Text>价格色</Text>
            </Chip>
            <Chip
              style={{ backgroundColor: colors.discount || '#4CAF50' }}
              textStyle={{ color: '#FFFFFF' }}
            >
              <Text>折扣色</Text>
            </Chip>
            <Chip
              style={{ backgroundColor: colors.rating || '#FFC107' }}
              textStyle={{ color: '#000000' }}
            >
              <Text>评分色</Text>
            </Chip>
            <Chip
              style={{ backgroundColor: colors.live || '#E91E63' }}
              textStyle={{ color: '#FFFFFF' }}
            >
              <Text>直播色</Text>
            </Chip>
          </View>
        </CardContent>
      </Card>

      {/* 字体演示 */}
      <Card style={styles.section}>
        <CardContent>
          <Text
            variant='titleMedium'
            style={{ color: colors.onSurface, marginBottom: spacing.md }}
          >
            字体系统
          </Text>

          <View style={styles.typographyDemo}>
            <Text variant='displaySmall' style={{ color: colors.onSurface }}>
              Display Small
            </Text>
            <Text variant='headlineMedium' style={{ color: colors.onSurface }}>
              Headline Medium
            </Text>
            <Text variant='titleLarge' style={{ color: colors.onSurface }}>
              Title Large
            </Text>
            <Text variant='bodyLarge' style={{ color: colors.onSurface }}>
              Body Large - 这是正文大字体
            </Text>
            <Text variant='bodyMedium' style={{ color: colors.onSurface }}>
              Body Medium - 这是正文中字体
            </Text>
            <Text variant='bodySmall' style={{ color: colors.onSurface }}>
              Body Small - 这是正文小字体
            </Text>
            <Text variant='labelLarge' style={{ color: colors.onSurface }}>
              Label Large
            </Text>
          </View>

          {/* 电商特定字体 */}
          <Divider style={{ marginVertical: spacing.md }} />
          <Text
            variant='bodyMedium'
            style={{ color: colors.onSurface, marginBottom: spacing.sm }}
          >
            电商特定字体
          </Text>
          <View style={styles.ecommerceTypography}>
            <Text style={[customStyles.ecommerce.priceText]}>¥199.99</Text>
            <Text style={[typography.caption, { color: colors.onSurface }]}>
              商品描述文字
            </Text>
          </View>
        </CardContent>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  header: {
    padding: 24,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
  },
  section: {
    marginBottom: 16,
  },
  themeControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  themeControl: {
    alignItems: 'center',
    gap: 8,
  },
  dynamicThemeSection: {
    alignItems: 'center',
  },
  testImage: {
    width: 120,
    height: 80,
    borderRadius: 8,
    marginVertical: 8,
  },
  dynamicThemeButtons: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  buttonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  buttonSizes: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  cardGrid: {
    flexDirection: 'row',
    gap: 8,
  },
  demoCard: {
    flex: 1,
  },
  interactiveCard: {
    borderWidth: 1,
    borderColor: 'transparent',
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  colorSwatch: {
    width: 80,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  colorLabel: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  ecommerceColors: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  typographyDemo: {
    gap: 8,
  },
  ecommerceTypography: {
    gap: 4,
  },
});

export default ThemeDemo;
