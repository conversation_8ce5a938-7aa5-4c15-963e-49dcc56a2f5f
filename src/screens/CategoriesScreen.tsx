/**
 * 分类页面
 * 展示商品分类和筛选功能
 */

import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Card, Searchbar, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useAppNavigation } from '../hooks/useAppNavigation';
import type { MainTabScreenProps } from '../navigation/types';
import { useTheme } from '../theme/ThemeContext';

type Props = MainTabScreenProps<'Categories'>;

const CategoriesScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();
  const { navigateToScreen } = useAppNavigation();
  const [searchQuery, setSearchQuery] = React.useState('');

  const categories = [
    { id: 1, name: '电子产品', icon: 'cellphone', color: '#FF6B6B' },
    { id: 2, name: '服装鞋帽', icon: 'tshirt-crew', color: '#4ECDC4' },
    { id: 3, name: '家居用品', icon: 'home', color: '#45B7D1' },
    { id: 4, name: '美妆护肤', icon: 'face-woman', color: '#F7DC6F' },
    { id: 5, name: '运动户外', icon: 'run', color: '#BB8FCE' },
    { id: 6, name: '食品饮料', icon: 'food', color: '#85C1E9' },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: 16,
      backgroundColor: theme.colors.surface,
    },
    searchBar: {
      marginBottom: 16,
    },
    content: {
      flex: 1,
      padding: 16,
    },
    categoriesGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    categoryCard: {
      width: '48%',
      marginBottom: 16,
      alignItems: 'center',
      padding: 20,
    },
    categoryIcon: {
      marginBottom: 12,
    },
    categoryName: {
      fontSize: 16,
      fontWeight: '500',
      textAlign: 'center',
      color: theme.colors.onSurface,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text
          variant='headlineSmall'
          style={{ color: theme.colors.onSurface, marginBottom: 16 }}
        >
          商品分类
        </Text>
        <Searchbar
          placeholder='搜索分类'
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          onSubmitEditing={() => {
            if (searchQuery.trim()) {
              navigateToScreen('Search', { query: searchQuery });
            }
          }}
        />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.categoriesGrid}>
          {categories.map(category => (
            <Card
              key={category.id}
              style={styles.categoryCard}
              onPress={() =>
                navigateToScreen('Search', { category: category.name })
              }
            >
              <Icon
                name={category.icon}
                size={48}
                color={category.color}
                style={styles.categoryIcon}
              />
              <Text style={styles.categoryName}>{category.name}</Text>
            </Card>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default CategoriesScreen;
