/**
 * 通知页面
 */

import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { Avatar, Card, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { RootStackScreenProps } from '../navigation/types';
import { useTheme } from '../theme/ThemeContext';

type Props = RootStackScreenProps<'Notifications'>;

const NotificationsScreen: React.FC<Props> = ({ navigation }) => {
  const { theme } = useTheme();

  const notifications = [
    {
      id: 1,
      title: '订单已发货',
      content: '您的订单已发货，请注意查收',
      time: '2小时前',
      type: 'order',
    },
    {
      id: 2,
      title: '新消息',
      content: '您有一条新的私信',
      time: '4小时前',
      type: 'message',
    },
    {
      id: 3,
      title: '系统通知',
      content: '应用已更新到最新版本',
      time: '1天前',
      type: 'system',
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: 16,
    },
    notificationCard: {
      marginBottom: 12,
      padding: 16,
    },
    notificationHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    notificationTitle: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
      marginLeft: 12,
      flex: 1,
    },
    notificationTime: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
    },
    notificationContent: {
      fontSize: 14,
      color: theme.colors.onSurface,
      marginLeft: 44,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        {notifications.map(notification => (
          <Card key={notification.id} style={styles.notificationCard}>
            <View style={styles.notificationHeader}>
              <Avatar.Icon size={32} icon='bell' />
              <Text style={styles.notificationTitle}>{notification.title}</Text>
              <Text style={styles.notificationTime}>{notification.time}</Text>
            </View>
            <Text style={styles.notificationContent}>
              {notification.content}
            </Text>
          </Card>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

export default NotificationsScreen;
