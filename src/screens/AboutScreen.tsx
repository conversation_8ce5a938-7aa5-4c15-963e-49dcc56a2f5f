/**
 * 关于页面
 */

import React from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import { Card, Text } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../theme/ThemeContext';

const AboutScreen: React.FC = () => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      padding: 16,
    },
    card: {
      padding: 20,
      marginBottom: 16,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
      textAlign: 'center',
      marginBottom: 16,
    },
    version: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 24,
    },
    description: {
      fontSize: 14,
      lineHeight: 20,
      color: theme.colors.onSurface,
      textAlign: 'center',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <Card style={styles.card}>
          <Text style={styles.title}>智能社交电商</Text>
          <Text style={styles.version}>版本 1.0.0</Text>
          <Text style={styles.description}>
            一个集购物、社交、智能推荐于一体的现代化电商应用。
            {'\n\n'}
            基于React Native 0.76.1新架构开发， 提供流畅的用户体验和丰富的功能。
          </Text>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

export default AboutScreen;
