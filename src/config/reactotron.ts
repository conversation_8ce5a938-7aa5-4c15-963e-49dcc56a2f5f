import Reactotron from 'reactotron-react-native';

if (__DEV__) {
  const tron = Reactotron.configure({
    name: 'SmartSocialEcommerceApp',
    host: 'localhost',
    port: 9090,
  })
    .useReactNative({
      asyncStorage: false,
      networking: {
        ignoreUrls: /symbolicate/,
      },
      editor: false,
      errors: { veto: () => false },
      overlay: false,
    })
    .connect();

  // 清除启动时的日志
  tron.clear?.();

  // 简化的console.tron实现
  (console as any).tron = {
    log: (...args: any[]) => tron.log && tron.log(args.join(' ')),
    warn: (...args: any[]) => tron.warn && tron.warn(args.join(' ')),
    error: (...args: any[]) => tron.error && tron.error(args.join(' '), ''),
    display: (config: any) => tron.display && tron.display(config),
    clear: () => tron.clear && tron.clear(),
  };
} else {
  // 生产环境下的空实现
  (console as any).tron = {
    log: () => {},
    warn: () => {},
    error: () => {},
    display: () => {},
    clear: () => {},
  };
}

export default Reactotron;
