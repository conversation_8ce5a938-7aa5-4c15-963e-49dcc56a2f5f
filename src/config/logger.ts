import { logger, consoleTransport } from 'react-native-logs';

// 日志级别定义
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
}

// 简化的日志配置
const defaultConfig: any = {
  severity: __DEV__ ? LogLevel.DEBUG : LogLevel.ERROR,
  transport: __DEV__ ? [consoleTransport] : [],
  transportOptions: {
    colors: {
      info: 'blueBright',
      warn: 'yellowBright',
      error: 'redBright',
    },
  },
  async: true,
  dateFormat: 'time',
  printLevel: true,
  printDate: true,
  enabled: true,
};

// 创建日志实例
const log = logger.createLogger(defaultConfig);

// 扩展日志功能
interface ExtendedLogger {
  debug: (message: string, ...args: unknown[]) => void;
  info: (message: string, ...args: unknown[]) => void;
  warn: (message: string, ...args: unknown[]) => void;
  error: (message: string, error?: Error, ...args: unknown[]) => void;
  api: (method: string, url: string, data?: unknown) => void;
  performance: (operation: string, duration: number) => void;
  user: (action: string, userId?: string, data?: unknown) => void;
}

export const Logger: ExtendedLogger = {
  debug: (message: string, ...args: unknown[]) => {
    log.debug(`[DEBUG] ${message}`, ...args);
    if (__DEV__ && console.tron) {
      console.tron.log(message, ...args);
    }
  },

  info: (message: string, ...args: unknown[]) => {
    log.info(`[INFO] ${message}`, ...args);
    if (__DEV__ && console.tron) {
      console.tron.log(message, ...args);
    }
  },

  warn: (message: string, ...args: unknown[]) => {
    log.warn(`[WARN] ${message}`, ...args);
    if (__DEV__ && console.tron) {
      console.tron.warn(message, ...args);
    }
  },

  error: (message: string, error?: Error, ...args: unknown[]) => {
    log.error(`[ERROR] ${message}`, error, ...args);
    if (__DEV__ && console.tron) {
      console.tron.error(message, error, ...args);
    }

    // 生产环境发送到Sentry
    if (!__DEV__ && error) {
      // Sentry.captureException(error);
    }
  },

  api: (method: string, url: string, data?: unknown) => {
    const message = `API ${method.toUpperCase()} ${url}`;
    log.debug(`[API] ${message}`, data);
    if (__DEV__ && console.tron) {
      console.tron.display({
        name: 'API Request',
        value: { method, url, data },
        preview: message,
      });
    }
  },

  performance: (operation: string, duration: number) => {
    const message = `${operation} took ${duration}ms`;
    log.info(`[PERF] ${message}`);
    if (__DEV__ && console.tron) {
      console.tron.display({
        name: 'Performance',
        value: { operation, duration },
        preview: message,
      });
    }
  },

  user: (action: string, userId?: string, data?: unknown) => {
    const message = `User ${userId || 'anonymous'} ${action}`;
    log.info(`[USER] ${message}`, data);
    if (__DEV__ && console.tron) {
      console.tron.display({
        name: 'User Action',
        value: { action, userId, data },
        preview: message,
      });
    }
  },
};

export default Logger;
