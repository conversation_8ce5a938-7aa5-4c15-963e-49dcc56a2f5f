import { startPerformanceMonitoring } from '../utils/performance';
import { Logger } from './logger';
import { initSentry } from './sentry';

// 调试工具初始化
export const initializeDebugTools = (): void => {
  Logger.info('Initializing debug tools...');

  // 1. 初始化Sentry（生产环境监控）
  try {
    initSentry();
    Logger.info('Sentry initialized successfully');
  } catch (error) {
    Logger.error('Failed to initialize Sentry', error as Error);
  }

  // 2. 初始化Reactotron（开发环境）
  if (__DEV__) {
    try {
      // Reactotron在其配置文件中自动初始化
      require('./reactotron');
      Logger.info('Reactotron initialized successfully');
    } catch (error) {
      Logger.error('Failed to initialize Reactotron', error as Error);
    }

    // 3. 启动性能监控
    try {
      startPerformanceMonitoring();
      Logger.info('Performance monitoring started');
    } catch (error) {
      Logger.error('Failed to start performance monitoring', error as Error);
    }

    // 4. 设置全局错误处理
    const originalConsoleError = console.error;
    console.error = (...args: unknown[]) => {
      Logger.error('Console Error', new Error(String(args[0])), ...args);
      originalConsoleError(...args);
    };

    // 5. 添加开发环境的调试信息
    Logger.info('Debug mode enabled', {
      platform: require('react-native').Platform.OS,
      version: require('react-native').Platform.Version,
      hermes: typeof HermesInternal === 'object' && HermesInternal !== null,
      newArchitecture: !!(global as any).RN$Bridgeless,
    });
  }

  Logger.info('Debug tools initialization completed');
};

// 开发环境调试辅助函数
export const debugUtils = {
  // 显示组件边界（开发环境）
  showComponentBoundaries: () => {
    if (__DEV__) {
      // 这里可以添加显示组件边界的逻辑
      Logger.info('Component boundaries visualization enabled');
    }
  },

  // 显示性能指标
  showPerformanceMetrics: () => {
    if (__DEV__) {
      const { performanceMonitor } = require('../utils/performance');
      const metrics = performanceMonitor.getAllMetrics();
      Logger.info('Performance Metrics', metrics);

      if (console.tron) {
        console.tron.display({
          name: 'Performance Metrics',
          value: metrics,
          preview: `${Object.keys(metrics).length} metrics available`,
        });
      }
    }
  },

  // 清除所有日志
  clearLogs: () => {
    if (__DEV__) {
      console.clear();
      if (console.tron) {
        console.tron.clear?.();
      }
      Logger.info('Logs cleared');
    }
  },

  // 模拟错误（测试用）
  simulateError: (message = 'Test error') => {
    if (__DEV__) {
      const error = new Error(message);
      Logger.error('Simulated error', error);
      throw error;
    }
  },

  // 内存使用情况
  logMemoryUsage: () => {
    if (__DEV__) {
      const { getMemoryUsage } = require('../utils/performance');
      const memory = getMemoryUsage();
      if (memory) {
        const usedMB = (memory.used / 1024 / 1024).toFixed(2);
        const totalMB = (memory.total / 1024 / 1024).toFixed(2);
        Logger.info(`Memory Usage: ${usedMB}MB / ${totalMB}MB`);
      }
    }
  },
};

// 全局调试对象（仅开发环境）
if (__DEV__) {
  (global as any).__DEBUG__ = debugUtils;
}
