import * as Sentry from '@sentry/react-native';

// Sentry配置接口
interface SentryConfig {
  dsn: string;
  debug: boolean;
  environment: string;
  tracesSampleRate: number;
  enableAutoSessionTracking: boolean;
  enableNativeCrashHandling: boolean;
}

// 环境配置
const getEnvironment = (): string => {
  if (__DEV__) return 'development';
  // 可以根据构建配置返回 'staging' 或 'production'
  return 'production';
};

// Sentry配置
const sentryConfig: SentryConfig = {
  dsn: process.env.SENTRY_DSN || '', // 从环境变量获取DSN
  debug: __DEV__,
  environment: getEnvironment(),
  tracesSampleRate: __DEV__ ? 1.0 : 0.1, // 开发环境100%，生产环境10%
  enableAutoSessionTracking: true,
  enableNativeCrashHandling: true,
};

// 初始化Sentry
export const initSentry = (): void => {
  if (!sentryConfig.dsn && !__DEV__) {
    console.warn('Sentry DSN not configured');
    return;
  }

  Sentry.init({
    dsn: sentryConfig.dsn,
    debug: sentryConfig.debug,
    environment: sentryConfig.environment,
    tracesSampleRate: sentryConfig.tracesSampleRate,
    enableAutoSessionTracking: sentryConfig.enableAutoSessionTracking,
    enableNativeCrashHandling: sentryConfig.enableNativeCrashHandling,

    beforeSend: (event, hint) => {
      // 开发环境不发送事件
      if (__DEV__) {
        console.log('Sentry Event (dev mode):', event);
        return null;
      }

      // 过滤掉一些不重要的错误
      if (event.exception) {
        const error = hint.originalException;
        if (error instanceof Error) {
          // 过滤网络错误等
          if (error.message.includes('Network request failed')) {
            return null;
          }
        }
      }

      return event;
    },

    // 基础集成，避免复杂的性能追踪配置
  });
};

// 性能监控辅助函数（简化版）
export const withSentryPerformance = <
  T extends (...args: unknown[]) => unknown,
>(
  name: string,
  fn: T,
): T =>
  ((...args: Parameters<T>) => {
    const startTime = Date.now();

    try {
      const result = fn(...args);

      // 如果是Promise，等待完成
      if (result instanceof Promise) {
        return result
          .then(res => {
            const duration = Date.now() - startTime;
            addSentryBreadcrumb(`${name} completed`, 'performance', 'info', {
              duration,
            });
            return res;
          })
          .catch(error => {
            const duration = Date.now() - startTime;
            addSentryBreadcrumb(`${name} failed`, 'performance', 'error', {
              duration,
            });
            throw error;
          });
      }

      const duration = Date.now() - startTime;
      addSentryBreadcrumb(`${name} completed`, 'performance', 'info', {
        duration,
      });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      addSentryBreadcrumb(`${name} failed`, 'performance', 'error', {
        duration,
      });
      throw error;
    }
  }) as T;

// 用户上下文设置
export const setSentryUser = (user: {
  id: string;
  email?: string;
  username?: string;
}): void => {
  Sentry.setUser(user);
};

// 添加面包屑
export const addSentryBreadcrumb = (
  message: string,
  category: string,
  level: 'info' | 'warning' | 'error' = 'info',
  data?: Record<string, unknown>,
): void => {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    data: data || {},
    timestamp: Date.now() / 1000,
  });
};

// 手动捕获异常
export const captureSentryException = (
  error: Error,
  context?: Record<string, unknown>,
): void => {
  if (context) {
    Sentry.withScope(scope => {
      Object.keys(context).forEach(key => {
        scope.setContext(key, context[key] as Record<string, unknown>);
      });
      Sentry.captureException(error);
    });
  } else {
    Sentry.captureException(error);
  }
};

export default Sentry;
