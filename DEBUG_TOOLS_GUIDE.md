# 调试工具使用指南

## 🛠️ 调试工具概览

本项目集成了一套完整的调试和监控工具，帮助开发者提高开发效率和应用质量。

### 📋 工具列表

1. **Flipper** - 主要调试平台
2. **Reactotron** - 状态和API调试
3. **Sentry** - 生产环境监控
4. **react-native-logs** - 增强日志系统
5. **Bundle Analyzer** - 包体积分析
6. **Performance Monitor** - 性能监控

## 🚀 快速开始

### 1. Flipper调试平台

#### 安装Flipper桌面应用

```bash
# macOS
brew install --cask flipper

# 或从官网下载
# https://fbflipper.com/
```

#### 使用方法

1. 启动Flipper桌面应用
2. 运行React Native应用
3. 在Flipper中查看连接的设备
4. 使用各种插件进行调试

#### 主要功能

- **Layout Inspector**: 查看组件层次结构
- **Network**: 监控网络请求
- **Logs**: 查看应用日志
- **React DevTools**: React组件调试
- **Databases**: 查看本地数据库

### 2. Reactotron状态调试

#### 安装Reactotron桌面应用

```bash
# macOS
brew install --cask reactotron

# 或从GitHub下载
# https://github.com/infinitered/reactotron/releases
```

#### 使用方法

1. 启动Reactotron桌面应用
2. 运行React Native应用
3. 应用会自动连接到Reactotron

#### 主要功能

- **State Management**: 查看和修改应用状态
- **API Monitoring**: 监控API请求和响应
- **AsyncStorage**: 查看本地存储数据
- **Custom Logs**: 自定义日志显示
- **Benchmarking**: 性能基准测试

#### 代码示例

```typescript
// 在代码中使用Reactotron
import { Logger } from '@/config/logger';

// 记录状态变化
Logger.debug('User state updated', { userId: '123', status: 'active' });

// 记录API请求
Logger.api('GET', '/api/users', { page: 1 });

// 记录用户行为
Logger.user('login', 'user123', { method: 'email' });
```

### 3. Sentry生产监控

#### 配置Sentry

1. 在Sentry官网创建项目
2. 获取DSN
3. 配置环境变量

```bash
# .env文件
SENTRY_DSN=https://<EMAIL>/project-id
```

#### 使用方法

```typescript
import { captureSentryException, setSentryUser } from '@/config/sentry';

// 设置用户信息
setSentryUser({
  id: 'user123',
  email: '<EMAIL>',
  username: 'john_doe',
});

// 手动捕获异常
try {
  // 可能出错的代码
} catch (error) {
  captureSentryException(error as Error, { context: 'user_action' });
}
```

### 4. 增强日志系统

#### 日志级别

- `DEBUG`: 调试信息（仅开发环境）
- `INFO`: 一般信息
- `WARN`: 警告信息
- `ERROR`: 错误信息

#### 使用示例

```typescript
import { Logger } from '@/config/logger';

// 基础日志
Logger.debug('Debug message');
Logger.info('Info message');
Logger.warn('Warning message');
Logger.error('Error message', new Error('Something went wrong'));

// 专用日志
Logger.api('POST', '/api/login', { username: 'user' });
Logger.performance('component_render', 150);
Logger.user('purchase', 'user123', { productId: 'prod456' });
```

### 5. 性能监控

#### 使用性能监控

```typescript
import { performanceMonitor, withPerformanceMonitoring } from '@/utils/performance';

// 手动计时
performanceMonitor.startTimer('data_processing');
// ... 执行代码
performanceMonitor.endTimer('data_processing');

// 函数装饰器
const processData = withPerformanceMonitoring('processData', (data) => {
  // 处理数据的代码
  return processedData;
});

// React Hook
import { usePerformanceMonitoring } from '@/utils/performance';

const MyComponent = () => {
  const perf = usePerformanceMonitoring('MyComponent');

  useEffect(() => {
    perf.onMount();
    return perf.onUnmount;
  }, []);

  return <View>...</View>;
};
```

### 6. Bundle分析

#### 分析包体积

```bash
# 分析整体包体积
npm run bundle:analyze

# 分析Android包
npm run bundle:analyze:android

# 分析iOS包
npm run bundle:analyze:ios
```

## 🔧 开发环境配置

### VS Code调试配置

已配置的调试选项：

- **Debug Android**: 调试Android应用
- **Debug iOS**: 调试iOS应用
- **Attach to packager**: 连接到Metro
- **Debug Jest Tests**: 调试Jest测试

### Chrome DevTools

1. 在模拟器中按 `Cmd+D` (iOS) 或 `Cmd+M` (Android)
2. 选择 "Debug"
3. 在Chrome中打开 `http://localhost:8081/debugger-ui/`
4. 打开Chrome DevTools进行调试

## 📊 监控指标

### 性能指标

- **启动时间**: 目标 < 3秒（冷启动）
- **页面切换**: 目标 < 300ms
- **FPS**: 目标 > 55fps
- **内存使用**: 目标 < 200MB
- **包体积**: 目标 < 50MB

### 错误监控

- **崩溃率**: 目标 < 0.05%
- **错误率**: 目标 < 1%
- **响应时间**: API请求 < 2秒

## 🚨 故障排除

### 常见问题

#### Flipper连接问题

1. 确保设备和电脑在同一网络
2. 检查防火墙设置
3. 重启Flipper和应用

#### Reactotron连接问题

1. 检查IP地址配置
2. 确保端口9090未被占用
3. 重启Reactotron应用

#### Sentry不工作

1. 检查DSN配置
2. 确认网络连接
3. 查看控制台错误信息

### 调试技巧

1. **使用断点**: 在Chrome DevTools中设置断点
2. **查看网络请求**: 使用Flipper Network插件
3. **监控状态变化**: 使用Reactotron State插件
4. **分析性能**: 使用Performance Monitor
5. **查看崩溃报告**: 在Sentry中分析错误

## 🎯 最佳实践

### 日志记录

- 使用适当的日志级别
- 包含足够的上下文信息
- 避免记录敏感信息
- 在生产环境中控制日志量

### 性能监控

- 监控关键用户路径
- 设置性能基线
- 定期分析性能数据
- 优化慢速操作

### 错误处理

- 捕获并记录所有错误
- 提供有意义的错误信息
- 实现优雅的错误恢复
- 监控错误趋势

### 调试流程

1. 复现问题
2. 收集日志和错误信息
3. 使用调试工具分析
4. 修复问题
5. 验证修复效果
6. 添加监控防止回归

## 📚 参考资源

- [Flipper官方文档](https://fbflipper.com/docs/)
- [Reactotron使用指南](https://github.com/infinitered/reactotron)
- [Sentry React Native文档](https://docs.sentry.io/platforms/react-native/)
- [React Native调试指南](https://reactnative.dev/docs/debugging)
- [Chrome DevTools文档](https://developers.google.com/web/tools/chrome-devtools)

---

**注意**: 调试工具主要用于开发环境，生产环境只启用必要的监控功能。
