# 智能社交电商App - 代码规范

## 📋 概述

本文档定义了智能社交电商App项目的代码规范，基于ESLint和Prettier配置，确保代码质量、一致性和可维护性。

## 🎯 核心原则

1. **一致性**: 所有代码遵循统一的格式和风格
2. **可读性**: 代码清晰易懂，便于维护和协作
3. **类型安全**: 充分利用TypeScript的类型系统
4. **性能优先**: 避免不必要的复杂度和性能问题
5. **最佳实践**: 遵循React Native和React的最佳实践

## 📝 格式化规范 (Prettier)

### 基础格式化

```javascript
// ✅ 正确
const config = {
  printWidth: 80, // 每行最大字符数
  tabWidth: 2, // 缩进宽度
  useTabs: false, // 使用空格而非制表符
  semi: true, // 语句末尾使用分号
  singleQuote: true, // 使用单引号
  trailingComma: 'all', // 尾随逗号
  bracketSpacing: true, // 对象括号内空格
  arrowParens: 'avoid', // 箭头函数参数括号
};

// ❌ 错误
const config = {
  printWidth: 80,
  tabWidth: 2,
  useTabs: false,
  semi: true,
  singleQuote: true,
  trailingComma: 'all',
  bracketSpacing: true,
  arrowParens: 'avoid',
};
```

### JSX格式化

```tsx
// ✅ 正确
const Component = ({ title, children }: Props) => (
  <View style={styles.container}>
    <Text style={styles.title}>{title}</Text>
    {children}
  </View>
);

// ❌ 错误
const Component = ({ title, children }: Props) => (
  <View style={styles.container}>
    <Text style={styles.title}>{title}</Text>
    {children}
  </View>
);
```

## 🔧 TypeScript规范

### 类型定义

```typescript
// ✅ 正确 - 使用interface而非type
interface User {
  id: string;
  name: string;
  email?: string;
}

// ❌ 错误 - 避免使用type定义对象
type User = {
  id: string;
  name: string;
  email?: string;
};

// ✅ 正确 - 禁止使用any
const processData = (data: unknown): string => {
  if (typeof data === 'string') {
    return data;
  }
  return String(data);
};

// ❌ 错误 - 禁止使用any
const processData = (data: any): string => {
  return data;
};

// ✅ 正确 - 禁止非空断言
const getValue = (obj: { value?: string }): string => {
  return obj.value ?? 'default';
};

// ❌ 错误 - 避免非空断言
const getValue = (obj: { value?: string }): string => {
  return obj.value!;
};
```

### 变量和函数

```typescript
// ✅ 正确 - 未使用的参数以_开头
const handleEvent = (_event: Event, data: string): void => {
  console.log(data);
};

// ✅ 正确 - 使用const而非let/var
const API_URL = 'https://api.example.com';
const users = ['alice', 'bob'];

// ❌ 错误 - 避免var
var API_URL = 'https://api.example.com';

// ✅ 正确 - 优先使用模板字符串
const message = `Hello, ${name}!`;

// ❌ 错误 - 避免字符串拼接
const message = 'Hello, ' + name + '!';
```

## ⚛️ React规范

### 组件定义

```tsx
// ✅ 正确 - 函数组件定义
interface ButtonProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
}

const Button = ({ title, onPress, disabled = false }: ButtonProps) => (
  <TouchableOpacity
    style={[styles.button, disabled && styles.disabled]}
    onPress={onPress}
    disabled={disabled}
  >
    <Text style={styles.buttonText}>{title}</Text>
  </TouchableOpacity>
);

// ✅ 正确 - 组件必须有displayName（用于调试）
Button.displayName = 'Button';

// ❌ 错误 - 避免匿名组件
export default ({ title, onPress }) => (
  <TouchableOpacity onPress={onPress}>
    <Text>{title}</Text>
  </TouchableOpacity>
);
```

### Hooks使用

```tsx
// ✅ 正确 - 遵循Hooks规则
const useCounter = (initialValue = 0) => {
  const [count, setCount] = useState(initialValue);

  const increment = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);

  return { count, increment };
};

// ✅ 正确 - 依赖数组完整
useEffect(() => {
  fetchData(userId);
}, [userId]); // 包含所有依赖

// ❌ 错误 - 缺少依赖
useEffect(() => {
  fetchData(userId);
}, []); // 缺少userId依赖
```

### JSX规范

```tsx
// ✅ 正确 - JSX中必须有key
{items.map(item => (
  <Item key={item.id} data={item} />
))}

// ❌ 错误 - 缺少key
{items.map(item => (
  <Item data={item} />
))}

// ✅ 正确 - 避免重复props
<Button title="Submit" disabled={false} />

// ❌ 错误 - 重复props
<Button title="Submit" title="Submit" />
```

## 📱 React Native规范

### 样式定义

```tsx
// ✅ 正确 - 使用StyleSheet
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
  },
});

// ❌ 错误 - 避免内联样式
<View style={{ flex: 1, backgroundColor: '#ffffff' }}>
  <Text style={{ fontSize: 18, color: '#333333' }}>Title</Text>
</View>;

// ❌ 错误 - 避免颜色字面量
const styles = StyleSheet.create({
  text: {
    color: 'red', // 应使用主题色彩
  },
});

// ✅ 正确 - 使用主题色彩
const styles = StyleSheet.create({
  text: {
    color: colors.primary,
  },
});
```

### 文本处理

```tsx
// ✅ 正确 - 所有文本必须包装在Text组件中
<View>
  <Text>这是正确的文本显示</Text>
</View>

// ❌ 错误 - 裸文本不能直接在View中
<View>
  这是错误的文本显示
</View>

// ✅ 正确 - 平台特定组件分离
// Button.ios.tsx
export const Button = () => <IOSButton />;

// Button.android.tsx
export const Button = () => <AndroidButton />;
```

### 样式管理

```tsx
// ✅ 正确 - 移除未使用的样式
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  // title: { fontSize: 18 }, // 已移除未使用的样式
});

// ✅ 正确 - 避免内联样式
<View style={styles.container}>
  <Text style={styles.title}>Title</Text>
</View>

// ❌ 错误 - 避免内联样式
<View style={{ flex: 1 }}>
  <Text style={{ fontSize: 18 }}>Title</Text>
</View>
```

## 🚀 性能和代码质量

### 复杂度控制

```typescript
// ✅ 正确 - 圈复杂度 < 10
const processUser = (user: User): ProcessedUser => {
  if (!user) return null;

  if (user.type === 'admin') {
    return processAdmin(user);
  }

  if (user.type === 'member') {
    return processMember(user);
  }

  return processGuest(user);
};

// ❌ 错误 - 复杂度过高
const processUser = (user: User): ProcessedUser => {
  if (!user) {
    if (user.id) {
      if (user.type === 'admin') {
        if (user.permissions) {
          if (user.permissions.read) {
            // 嵌套过深，复杂度过高
          }
        }
      }
    }
  }
  // ...
};
```

### 函数长度限制

```typescript
// ✅ 正确 - 函数长度 < 50行，参数 < 4个
const createUser = (name: string, email: string, role: UserRole): User => {
  // 实现逻辑（少于50行）
  return {
    id: generateId(),
    name,
    email,
    role,
    createdAt: new Date(),
  };
};

// ❌ 错误 - 参数过多
const createUser = (
  name: string,
  email: string,
  role: UserRole,
  department: string,
  manager: string,
  startDate: Date,
  // ... 更多参数
) => {
  // 应该使用对象参数或分解为多个函数
};
```

### 导入和导出

```typescript
// ✅ 正确 - 避免重复导入
import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';

// ❌ 错误 - 重复导入
import React from 'react';
import { useState, useEffect } from 'react';

// ✅ 正确 - 使用对象解构
const { name, email } = user;

// ✅ 正确 - 避免不必要的重命名
import { formatDate } from '@/utils';

// ❌ 错误 - 不必要的重命名
import { formatDate as formatDateUtil } from '@/utils';
```

## 🛡️ 错误处理和调试

### 控制台和调试

```typescript
// ✅ 正确 - 使用适当的日志级别
console.warn('This is a warning message');
logger.info('User logged in', { userId: user.id });

// ❌ 错误 - 避免console.log在生产代码中
console.log('Debug info'); // 应该移除或使用logger

// ❌ 错误 - 禁止debugger和alert
debugger; // 生产代码中禁止
alert('Error occurred'); // 使用适当的错误处理
```

### 错误处理

```typescript
// ✅ 正确 - 适当的错误处理
const fetchUserData = async (userId: string): Promise<User | null> => {
  try {
    const response = await api.getUser(userId);
    return response.data;
  } catch (error) {
    logger.error('Failed to fetch user data', { userId, error });
    return null;
  }
};

// ✅ 正确 - 一致的返回类型
const processData = (data: string): string => {
  if (!data) {
    return '';
  }

  if (data.length > 100) {
    return data.substring(0, 100);
  }

  return data;
};
```

## 📁 文件和项目结构

### 文件命名

```
// ✅ 正确的文件结构
src/
├── components/
│   ├── Button/
│   │   ├── Button.tsx
│   │   ├── Button.test.tsx
│   │   └── index.ts
│   └── index.ts
├── screens/
│   ├── HomeScreen/
│   │   ├── HomeScreen.tsx
│   │   └── index.ts
├── services/
│   ├── api.ts
│   └── storage.ts
├── types/
│   └── index.ts
└── utils/
    └── index.ts
```

### 导入路径

```typescript
// ✅ 正确 - 使用路径映射
import { Button } from '@/components';
import { User } from '@/types';
import { formatDate } from '@/utils';

// ❌ 错误 - 相对路径过长
import { Button } from '../../../components/Button';
```

## 🧪 测试规范

### 测试文件结构

```typescript
// Button.test.tsx
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Button } from './Button';

describe('Button', () => {
  it('should render correctly', () => {
    const { getByText } = render(
      <Button title="Test Button" onPress={() => {}} />
    );

    expect(getByText('Test Button')).toBeTruthy();
  });

  it('should call onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <Button title="Test Button" onPress={mockOnPress} />
    );

    fireEvent.press(getByText('Test Button'));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });
});
```

## 📊 代码质量指标

### 必须达到的标准

- **ESLint错误**: 0个
- **ESLint警告**: 0个（严格模式）
- **TypeScript错误**: 0个
- **测试覆盖率**: >80%
- **圈复杂度**: <10
- **函数长度**: <50行
- **文件长度**: <300行
- **函数参数**: <4个

### 性能要求

- **包体积**: <50MB
- **启动时间**: 冷启动<3秒，热启动<1秒
- **页面切换**: <300ms
- **列表滚动**: 60fps
- **内存使用**: <200MB

## 🔄 代码审查检查清单

### 提交前检查

- [ ] 运行 `npm run validate` 通过
- [ ] 所有测试通过
- [ ] 代码格式化正确
- [ ] 没有console.log等调试代码
- [ ] 类型定义完整
- [ ] 组件有适当的props类型
- [ ] 错误处理完善
- [ ] 性能考虑充分

### 代码审查要点

- [ ] 代码逻辑清晰
- [ ] 命名有意义
- [ ] 没有重复代码
- [ ] 遵循单一职责原则
- [ ] 适当的注释
- [ ] 安全性考虑
- [ ] 可维护性良好

## 🛠️ 工具集成

### VS Code设置

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

### Git Hooks

- **pre-commit**: 运行lint-staged检查
- **commit-msg**: 验证提交消息格式

## 📚 参考资源

- [React Native官方文档](https://reactnative.dev/)
- [TypeScript官方文档](https://www.typescriptlang.org/)
- [ESLint规则文档](https://eslint.org/docs/rules/)
- [Prettier配置文档](https://prettier.io/docs/en/configuration.html)
- [React Hooks规则](https://reactjs.org/docs/hooks-rules.html)

---

**注意**: 此规范是项目的强制性标准，所有代码必须严格遵循。如有疑问或建议，请通过团队沟通渠道讨论。
