# 智能社交电商 App - 项目配置文档

## 技术栈

- **React Native**: 0.76.1 (新架构启用)
- **React**: 18.3.1
- **TypeScript**: 5.7.2 (严格模式)
- **Metro**: 0.81.0
- **Hermes**: 启用
- **新架构**: Fabric + TurboModules 启用

## 项目结构

```
src/
├── components/     # 可复用组件
├── screens/        # 页面组件
├── services/       # API服务
├── hooks/          # 自定义Hooks
├── utils/          # 工具函数
├── types/          # TypeScript类型定义
├── store/          # 状态管理
└── navigation/     # 导航配置
```

## 开发环境要求

- Node.js 18 LTS
- Xcode (iOS 开发)
- Android Studio (Android 开发)
- CocoaPods (iOS 依赖管理)

## 新架构配置

### Android

- `android/gradle.properties` 中 `newArchEnabled=true`
- `hermesEnabled=true`

### iOS

- Podfile 已配置支持新架构
- CocoaPods 依赖已安装

## TypeScript 配置

- 启用严格模式
- 配置路径映射 (@/_ 指向 src/_)
- 启用所有严格检查选项

## 运行项目

```bash
# 启动Metro
npm start

# 运行iOS
npm run ios

# 运行Android
npm run android
```

## 下一步

1. 配置 Git 工作流和团队协作
2. 设置代码规范和质量控制
3. 集成调试工具链
4. 开始 UI 框架开发
