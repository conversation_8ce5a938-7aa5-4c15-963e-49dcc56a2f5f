# Auto detect text files and perform LF normalization
* text=auto

# Source code
*.js text eol=lf
*.jsx text eol=lf
*.ts text eol=lf
*.tsx text eol=lf
*.json text eol=lf
*.md text eol=lf
*.yml text eol=lf
*.yaml text eol=lf

# Config files
*.config.js text eol=lf
*.config.ts text eol=lf
.gitignore text eol=lf
.gitattributes text eol=lf

# Documentation
*.txt text eol=lf
*.md text eol=lf
LICENSE text eol=lf
README* text eol=lf

# Graphics
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg text eol=lf

# Archives
*.7z binary
*.gz binary
*.tar binary
*.zip binary

# Fonts
*.ttf binary
*.eot binary
*.otf binary
*.woff binary
*.woff2 binary

# Executables
*.exe binary
*.pyc binary

# RC files (like .babelrc or .eslintrc)
.*rc text eol=lf

# Ignore files (like .npmignore or .gitignore)
.*ignore text eol=lf