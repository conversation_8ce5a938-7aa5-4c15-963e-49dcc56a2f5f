// Jest测试环境设置

// Mock调试工具
jest.mock('./src/config/debug', () => ({
  initializeDebugTools: jest.fn(),
  debugUtils: {
    showComponentBoundaries: jest.fn(),
    showPerformanceMetrics: jest.fn(),
    clearLogs: jest.fn(),
    simulateError: jest.fn(),
    logMemoryUsage: jest.fn(),
  },
}));

// Mock Logger
jest.mock('./src/config/logger', () => ({
  Logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    api: jest.fn(),
    performance: jest.fn(),
    user: jest.fn(),
  },
}));

// Mock Sentry
jest.mock('./src/config/sentry', () => ({
  initSentry: jest.fn(),
  setSentryUser: jest.fn(),
  addSentryBreadcrumb: jest.fn(),
  captureSentryException: jest.fn(),
  withSentryPerformance: (name, fn) => fn,
}));

// Mock Reactotron
jest.mock('./src/config/reactotron', () => ({}));

// Mock性能监控
jest.mock('./src/utils/performance', () => ({
  performanceMonitor: {
    startTimer: jest.fn(),
    endTimer: jest.fn(),
    getMetrics: jest.fn(),
    clearMetrics: jest.fn(),
    getAllMetrics: jest.fn(),
  },
  withPerformanceMonitoring: (name, fn) => fn,
  usePerformanceMonitoring: () => ({
    onMount: jest.fn(),
    onUnmount: jest.fn(),
    measureRender: jest.fn(() => jest.fn()),
  }),
  getMemoryUsage: jest.fn(() => null),
  fpsMonitor: {
    start: jest.fn(),
    stop: jest.fn(),
    getCurrentFPS: jest.fn(() => 60),
  },
  startPerformanceMonitoring: jest.fn(),
  stopPerformanceMonitoring: jest.fn(),
}));

// 全局变量Mock
global.__DEV__ = true;
global.HermesInternal = null;

// Console.tron Mock
console.tron = {
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  display: jest.fn(),
  clear: jest.fn(),
};
