# Resolve react_native_pods.rb with node to allow for hoisting
require Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__]).strip

platform :ios, min_ios_version_supported
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'SmartSocialEcommerceApp' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # Disable new architecture temporarily to fix gesture handler issues
    :fabric_enabled => false,
    :new_arch_enabled => false,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  # 确保手势处理器和屏幕导航正确链接
  pod 'RNGestureHandler', :path => '../node_modules/react-native-gesture-handler'
  pod 'RNScreens', :path => '../node_modules/react-native-screens'

  target 'SmartSocialEcommerceAppTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end
